"""
Main window for the Warehouse Inventory Management Desktop Application
"""
import sys
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QMenuBar, QStatusBar, QToolBar, QLabel, QPushButton, QMessageBox,
    QSplitter, QFrame, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QAction, QIcon, QPixmap, QFont

from src.core.config import config
from src.core.logger import get_logger
from src.ui.widgets.inventory_widget import InventoryWidget
from src.ui.widgets.orders_widget import OrdersWidget
from src.ui.widgets.dashboard_widget import DashboardWidget
from src.ui.widgets.scanner_widget import ScannerWidget
from src.ui.widgets.reports_widget import ReportsWidget
from src.ui.dialogs.login_dialog import LoginDialog
from src.ui.dialogs.settings_dialog import SettingsDialog
from src.services.api_client import APIClient
from src.services.sync_service import SyncService

logger = get_logger(__name__)


class MainWindow(QMainWindow):
    """Main application window"""
    
    # Signals
    user_logged_in = pyqtSignal(dict)
    user_logged_out = pyqtSignal()
    sync_status_changed = pyqtSignal(str)
    
    def __init__(self, api_available: bool = True):
        super().__init__()
        
        self.api_available = api_available
        self.current_user = None
        self.api_client = None
        self.sync_service = None
        
        # Initialize UI
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # Setup services
        self.setup_services()
        
        # Setup timers
        self.setup_timers()
        
        # Connect signals
        self.connect_signals()
        
        # Show login dialog if API is available
        if self.api_available:
            self.show_login_dialog()
        else:
            self.show_offline_mode_message()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.setGeometry(100, 100, config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # Set minimum size
        self.setMinimumSize(800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create header
        self.create_header(main_layout)
        
        # Create main content area
        self.create_main_content(main_layout)
    
    def create_header(self, parent_layout):
        """Create the header section"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setMaximumHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Logo/Title
        title_label = QLabel(config.APP_NAME)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        # User info
        self.user_label = QLabel("Not logged in")
        self.user_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        
        # Connection status
        self.connection_label = QLabel("Offline" if not self.api_available else "Online")
        self.connection_label.setStyleSheet(
            "color: red;" if not self.api_available else "color: green;"
        )
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.connection_label)
        header_layout.addWidget(QLabel(" | "))
        header_layout.addWidget(self.user_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout):
        """Create the main content area with tabs"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        
        # Create tabs
        self.dashboard_widget = DashboardWidget()
        self.inventory_widget = InventoryWidget()
        self.orders_widget = OrdersWidget()
        self.scanner_widget = ScannerWidget()
        self.reports_widget = ReportsWidget()
        
        # Add tabs
        self.tab_widget.addTab(self.dashboard_widget, "Dashboard")
        self.tab_widget.addTab(self.inventory_widget, "Inventory")
        self.tab_widget.addTab(self.orders_widget, "Orders")
        self.tab_widget.addTab(self.scanner_widget, "Scanner")
        self.tab_widget.addTab(self.reports_widget, "Reports")
        
        parent_layout.addWidget(self.tab_widget)
    
    def setup_menu_bar(self):
        """Setup the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        # Login/Logout
        self.login_action = QAction("Login", self)
        self.login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(self.login_action)
        
        self.logout_action = QAction("Logout", self)
        self.logout_action.triggered.connect(self.logout)
        self.logout_action.setEnabled(False)
        file_menu.addAction(self.logout_action)
        
        file_menu.addSeparator()
        
        # Settings
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.show_settings_dialog)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        # Exit
        exit_action = QAction("Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools")
        
        # Sync
        sync_action = QAction("Sync Now", self)
        sync_action.triggered.connect(self.sync_now)
        tools_menu.addAction(sync_action)
        
        # Scanner
        scanner_action = QAction("Open Scanner", self)
        scanner_action.triggered.connect(self.open_scanner)
        tools_menu.addAction(scanner_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup the toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Quick actions
        refresh_action = QAction("Refresh", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        scan_action = QAction("Scan", self)
        scan_action.triggered.connect(self.open_scanner)
        toolbar.addAction(scan_action)
        
        toolbar.addSeparator()
        
        sync_action = QAction("Sync", self)
        sync_action.triggered.connect(self.sync_now)
        toolbar.addAction(sync_action)
    
    def setup_status_bar(self):
        """Setup the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status message
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # Sync status
        self.sync_status_label = QLabel("Not synced")
        self.status_bar.addPermanentWidget(self.sync_status_label)
    
    def setup_services(self):
        """Setup application services"""
        if self.api_available:
            self.api_client = APIClient(config.API_BASE_URL)
            self.sync_service = SyncService(self.api_client)
    
    def setup_timers(self):
        """Setup application timers"""
        # Auto-sync timer
        if config.AUTO_SYNC and self.api_available:
            self.sync_timer = QTimer()
            self.sync_timer.timeout.connect(self.auto_sync)
            self.sync_timer.start(config.SYNC_INTERVAL_MINUTES * 60 * 1000)
        
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # Update every 5 seconds
    
    def connect_signals(self):
        """Connect application signals"""
        self.user_logged_in.connect(self.on_user_logged_in)
        self.user_logged_out.connect(self.on_user_logged_out)
        self.sync_status_changed.connect(self.on_sync_status_changed)
    
    def show_login_dialog(self):
        """Show the login dialog"""
        if not self.api_available:
            return
        
        dialog = LoginDialog(self.api_client, self)
        if dialog.exec() == LoginDialog.DialogCode.Accepted:
            user_data = dialog.get_user_data()
            self.current_user = user_data
            self.user_logged_in.emit(user_data)
    
    def logout(self):
        """Logout the current user"""
        self.current_user = None
        self.user_logged_out.emit()
    
    def show_settings_dialog(self):
        """Show the settings dialog"""
        dialog = SettingsDialog(self)
        dialog.exec()
    
    def show_about_dialog(self):
        """Show the about dialog"""
        QMessageBox.about(
            self,
            "About",
            f"{config.APP_NAME} v{config.APP_VERSION}\n\n"
            "A comprehensive inventory management system for small warehouses.\n\n"
            "Features:\n"
            "• Real-time inventory tracking\n"
            "• Barcode scanning\n"
            "• Order management\n"
            "• Automated reporting\n"
            "• Offline support"
        )
    
    def show_offline_mode_message(self):
        """Show offline mode message"""
        self.status_label.setText("Running in offline mode - API not available")
        logger.warning("Application started in offline mode")
    
    def on_user_logged_in(self, user_data):
        """Handle user login"""
        self.user_label.setText(f"Welcome, {user_data.get('full_name', 'User')}")
        self.login_action.setEnabled(False)
        self.logout_action.setEnabled(True)
        self.status_label.setText("Logged in successfully")
        
        # Update widgets with user data
        for i in range(self.tab_widget.count()):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'set_user'):
                widget.set_user(user_data)
        
        logger.info(f"User logged in: {user_data.get('username')}")
    
    def on_user_logged_out(self):
        """Handle user logout"""
        self.user_label.setText("Not logged in")
        self.login_action.setEnabled(True)
        self.logout_action.setEnabled(False)
        self.status_label.setText("Logged out")
        
        logger.info("User logged out")
    
    def on_sync_status_changed(self, status):
        """Handle sync status change"""
        self.sync_status_label.setText(f"Sync: {status}")
    
    def refresh_data(self):
        """Refresh all data"""
        self.status_label.setText("Refreshing data...")
        
        # Refresh current tab
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, 'refresh'):
            current_widget.refresh()
        
        self.status_label.setText("Data refreshed")
    
    def sync_now(self):
        """Trigger immediate sync"""
        if not self.sync_service:
            return
        
        self.progress_bar.setVisible(True)
        self.status_label.setText("Syncing...")
        
        # TODO: Implement sync in background thread
        # For now, just simulate sync
        QTimer.singleShot(2000, self.sync_completed)
    
    def sync_completed(self):
        """Handle sync completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Sync completed")
        self.sync_status_changed.emit("Completed")
    
    def auto_sync(self):
        """Perform automatic sync"""
        if self.current_user and self.sync_service:
            self.sync_now()
    
    def update_status(self):
        """Update application status"""
        # Update connection status
        if self.api_available and self.api_client:
            # TODO: Check API health
            pass
    
    def open_scanner(self):
        """Open the scanner tab"""
        scanner_index = self.tab_widget.indexOf(self.scanner_widget)
        self.tab_widget.setCurrentIndex(scanner_index)
    
    def closeEvent(self, event):
        """Handle application close event"""
        reply = QMessageBox.question(
            self,
            "Exit Application",
            "Are you sure you want to exit?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            logger.info("Application closing")
            event.accept()
        else:
            event.ignore()
