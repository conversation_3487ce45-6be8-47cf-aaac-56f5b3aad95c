#!/bin/bash
# Linux/Mac Shell Script to Start Warehouse Inventory Management System
# Run this script to start the system automatically

echo "========================================"
echo " WAREHOUSE INVENTORY MANAGEMENT SYSTEM"
echo "========================================"
echo
echo "Starting the system..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed"
        echo "Please install Python 3.11+ from https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 11 ]); then
    echo "ERROR: Python 3.11+ is required. Found Python $PYTHON_VERSION"
    echo "Please install Python 3.11+ from https://python.org"
    exit 1
fi

# Make the script executable
chmod +x quick_start.py

# Run the quick start script
echo "Running quick start script..."
$PYTHON_CMD quick_start.py

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Setup failed. Please check the errors above."
    exit 1
fi

echo
echo "========================================"
echo " SYSTEM STARTED SUCCESSFULLY!"
echo "========================================"
echo
echo "The system is now running. Check the output above for access URLs."
echo
