"""
Main entry point for the Warehouse Inventory Management Desktop Application
"""
import sys
import os
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt, QDir
from PyQt6.QtGui import QIcon

from src.ui.main_window import MainWindow
from src.core.config import AppConfig
from src.core.logger import setup_logging
from src.services.api_client import APIClient
from src.core.database import init_local_db

# Configure logging
logger = setup_logging()


def setup_application():
    """Setup the QApplication with proper configuration"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Warehouse Inventory Management")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Warehouse Solutions")
    app.setOrganizationDomain("warehouse-solutions.com")
    
    # Set application icon
    icon_path = Path(__file__).parent / "resources" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Set application style
    app.setStyle("Fusion")  # Modern cross-platform style
    
    # Enable high DPI scaling
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    return app


def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import cv2
        import pyzbar
        from PIL import Image
        import requests
        logger.info("All dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        return False


def check_api_connection():
    """Check if the backend API is accessible"""
    try:
        config = AppConfig()
        api_client = APIClient(config.API_BASE_URL)
        
        # Test connection with health check
        response = api_client.get("/health")
        if response and response.get("status") == "healthy":
            logger.info("API connection successful")
            return True
        else:
            logger.warning("API health check failed")
            return False
    except Exception as e:
        logger.error(f"API connection failed: {e}")
        return False


def show_startup_error(message: str):
    """Show startup error dialog"""
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Icon.Critical)
    msg_box.setWindowTitle("Startup Error")
    msg_box.setText("Failed to start the application")
    msg_box.setDetailedText(message)
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.exec()


def main():
    """Main application entry point"""
    try:
        logger.info("Starting Warehouse Inventory Management Desktop Application")
        
        # Check dependencies
        if not check_dependencies():
            show_startup_error(
                "Some required dependencies are missing. "
                "Please ensure all packages from requirements.txt are installed."
            )
            return 1
        
        # Setup QApplication
        app = setup_application()
        
        # Initialize local database
        try:
            init_local_db()
            logger.info("Local database initialized")
        except Exception as e:
            logger.error(f"Failed to initialize local database: {e}")
            show_startup_error(f"Failed to initialize local database: {e}")
            return 1
        
        # Check API connection (non-blocking)
        api_available = check_api_connection()
        if not api_available:
            logger.warning("API is not available - running in offline mode")
        
        # Create and show main window
        try:
            main_window = MainWindow(api_available=api_available)
            main_window.show()
            
            logger.info("Application started successfully")
            
            # Start the event loop
            return app.exec()
            
        except Exception as e:
            logger.error(f"Failed to create main window: {e}")
            show_startup_error(f"Failed to create main window: {e}")
            return 1
            
    except Exception as e:
        logger.error(f"Unexpected error during startup: {e}")
        show_startup_error(f"Unexpected error during startup: {e}")
        return 1
    
    finally:
        logger.info("Application shutdown")


if __name__ == "__main__":
    # Ensure proper exit code
    exit_code = main()
    sys.exit(exit_code)
