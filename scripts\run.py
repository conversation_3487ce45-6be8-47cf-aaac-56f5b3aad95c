#!/usr/bin/env python3
"""
Run script for Warehouse Inventory Management System
This script provides easy commands to run different parts of the system
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, cwd=None, check=True, background=False):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        if background:
            # Run in background
            subprocess.Popen(command, shell=True, cwd=cwd)
            return None
        else:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd, 
                check=check
            )
            return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if check:
            sys.exit(1)
        return e


def run_backend():
    """Run the backend API server"""
    print("🚀 Starting Backend API Server...")
    backend_dir = Path('backend')
    
    if not backend_dir.exists():
        print("Error: Backend directory not found")
        return False
    
    # Check if virtual environment exists
    venv_path = backend_dir / 'venv'
    if venv_path.exists():
        if os.name == 'nt':  # Windows
            python_cmd = str(venv_path / 'Scripts' / 'python')
        else:  # Linux/Mac
            python_cmd = str(venv_path / 'bin' / 'python')
    else:
        python_cmd = 'python'
    
    # Run the backend
    run_command(f"{python_cmd} -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", cwd=backend_dir)
    return True


def run_desktop():
    """Run the desktop application"""
    print("🖥️ Starting Desktop Application...")
    desktop_dir = Path('desktop')
    
    if not desktop_dir.exists():
        print("Error: Desktop directory not found")
        return False
    
    # Check if virtual environment exists
    venv_path = desktop_dir / 'venv'
    if venv_path.exists():
        if os.name == 'nt':  # Windows
            python_cmd = str(venv_path / 'Scripts' / 'python')
        else:  # Linux/Mac
            python_cmd = str(venv_path / 'bin' / 'python')
    else:
        python_cmd = 'python'
    
    # Run the desktop app
    run_command(f"{python_cmd} main.py", cwd=desktop_dir)
    return True


def run_mobile():
    """Run the mobile application"""
    print("📱 Starting Mobile Application...")
    mobile_dir = Path('mobile')
    
    if not mobile_dir.exists():
        print("Error: Mobile directory not found")
        return False
    
    # Check if virtual environment exists
    venv_path = mobile_dir / 'venv'
    if venv_path.exists():
        if os.name == 'nt':  # Windows
            python_cmd = str(venv_path / 'Scripts' / 'python')
        else:  # Linux/Mac
            python_cmd = str(venv_path / 'bin' / 'python')
    else:
        python_cmd = 'python'
    
    # Run the mobile app
    run_command(f"{python_cmd} main.py", cwd=mobile_dir)
    return True


def run_docker():
    """Run the system using Docker"""
    print("🐳 Starting System with Docker...")
    
    # Start all services
    run_command("docker-compose up -d")
    
    print("\n✅ System started successfully!")
    print("\nServices:")
    print("- Backend API: http://localhost:8000")
    print("- API Documentation: http://localhost:8000/docs")
    print("- Grafana Dashboard: http://localhost:3000 (admin/admin)")
    print("- Flower (Celery Monitor): http://localhost:5555")
    print("- Prometheus: http://localhost:9090")
    
    return True


def stop_docker():
    """Stop Docker services"""
    print("🛑 Stopping Docker services...")
    run_command("docker-compose down")
    print("✅ Docker services stopped")


def run_tests():
    """Run tests for all components"""
    print("🧪 Running Tests...")
    
    # Backend tests
    backend_dir = Path('backend')
    if backend_dir.exists():
        print("\n--- Backend Tests ---")
        venv_path = backend_dir / 'venv'
        if venv_path.exists():
            if os.name == 'nt':  # Windows
                pytest_cmd = str(venv_path / 'Scripts' / 'pytest')
            else:  # Linux/Mac
                pytest_cmd = str(venv_path / 'bin' / 'pytest')
        else:
            pytest_cmd = 'pytest'
        
        run_command(f"{pytest_cmd} tests/ -v", cwd=backend_dir, check=False)
    
    # Desktop tests
    desktop_dir = Path('desktop')
    if desktop_dir.exists():
        print("\n--- Desktop Tests ---")
        venv_path = desktop_dir / 'venv'
        if venv_path.exists():
            if os.name == 'nt':  # Windows
                pytest_cmd = str(venv_path / 'Scripts' / 'pytest')
            else:  # Linux/Mac
                pytest_cmd = str(venv_path / 'bin' / 'pytest')
        else:
            pytest_cmd = 'pytest'
        
        run_command(f"{pytest_cmd} tests/ -v", cwd=desktop_dir, check=False)
    
    # Mobile tests
    mobile_dir = Path('mobile')
    if mobile_dir.exists():
        print("\n--- Mobile Tests ---")
        venv_path = mobile_dir / 'venv'
        if venv_path.exists():
            if os.name == 'nt':  # Windows
                pytest_cmd = str(venv_path / 'Scripts' / 'pytest')
            else:  # Linux/Mac
                pytest_cmd = str(venv_path / 'bin' / 'pytest')
        else:
            pytest_cmd = 'pytest'
        
        run_command(f"{pytest_cmd} tests/ -v", cwd=mobile_dir, check=False)


def show_status():
    """Show system status"""
    print("📊 System Status")
    print("=" * 30)
    
    # Check Docker services
    result = run_command("docker-compose ps", check=False)
    if result and result.returncode == 0:
        print("\n🐳 Docker Services:")
        run_command("docker-compose ps", check=False)
    
    # Check if backend is running
    result = run_command("curl -s http://localhost:8000/health", check=False)
    if result and result.returncode == 0:
        print("\n✅ Backend API is running")
    else:
        print("\n❌ Backend API is not running")
    
    # Check database connection
    result = run_command("docker-compose exec -T postgres pg_isready -U warehouse", check=False)
    if result and result.returncode == 0:
        print("✅ Database is running")
    else:
        print("❌ Database is not running")
    
    # Check Redis
    result = run_command("docker-compose exec -T redis redis-cli ping", check=False)
    if result and result.returncode == 0:
        print("✅ Redis is running")
    else:
        print("❌ Redis is not running")


def main():
    """Main run function"""
    parser = argparse.ArgumentParser(description='Run Warehouse Inventory Management System')
    parser.add_argument('command', choices=[
        'backend', 'desktop', 'mobile', 'docker', 'stop', 'test', 'status', 'all'
    ], help='Component to run')
    
    args = parser.parse_args()
    
    print("🏭 Warehouse Inventory Management System")
    print("=" * 50)
    
    if args.command == 'backend':
        run_backend()
    elif args.command == 'desktop':
        run_desktop()
    elif args.command == 'mobile':
        run_mobile()
    elif args.command == 'docker':
        run_docker()
    elif args.command == 'stop':
        stop_docker()
    elif args.command == 'test':
        run_tests()
    elif args.command == 'status':
        show_status()
    elif args.command == 'all':
        print("🚀 Starting all components...")
        
        # Start Docker services first
        run_docker()
        
        print("\nWaiting for services to be ready...")
        import time
        time.sleep(5)
        
        # Start desktop app in background
        print("\n🖥️ Starting Desktop Application...")
        run_command("python scripts/run.py desktop", background=True)
        
        # Start mobile app
        print("\n📱 Starting Mobile Application...")
        run_command("python scripts/run.py mobile", background=True)
        
        print("\n✅ All components started!")
        print("\nPress Ctrl+C to stop all services")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping all services...")
            stop_docker()


if __name__ == "__main__":
    main()
