@echo off
REM Windows Batch Script to Start Warehouse Inventory Management System
REM Double-click this file to start the system automatically

echo ========================================
echo  WAREHOUSE INVENTORY MANAGEMENT SYSTEM
echo ========================================
echo.
echo Starting the system...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://python.org
    pause
    exit /b 1
)

REM Run the quick start script
echo Running quick start script...
python quick_start.py

if errorlevel 1 (
    echo.
    echo ERROR: Setup failed. Please check the errors above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  SYSTEM STARTED SUCCESSFULLY!
echo ========================================
echo.
echo The system is now running. Check the output above for access URLs.
echo.
pause
