"""
WebSocket connection manager for real-time updates
"""
from fastapi import WebSocket
from typing import Dict, List
import json
import structlog

logger = structlog.get_logger()


class WebSocketManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.room_connections: Dict[str, List[str]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info("WebSocket client connected", client_id=client_id)
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            
        # Remove from all rooms
        for room_id, clients in self.room_connections.items():
            if client_id in clients:
                clients.remove(client_id)
                
        logger.info("WebSocket client disconnected", client_id=client_id)
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(message)
            except Exception as e:
                logger.error("Failed to send personal message", 
                           client_id=client_id, error=str(e))
                self.disconnect(client_id)
    
    async def send_json_message(self, data: dict, client_id: str):
        """Send a JSON message to a specific client"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_json(data)
            except Exception as e:
                logger.error("Failed to send JSON message", 
                           client_id=client_id, error=str(e))
                self.disconnect(client_id)
    
    async def broadcast_message(self, message: str):
        """Broadcast a message to all connected clients"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error("Failed to broadcast message", 
                           client_id=client_id, error=str(e))
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def broadcast_json(self, data: dict):
        """Broadcast a JSON message to all connected clients"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_json(data)
            except Exception as e:
                logger.error("Failed to broadcast JSON", 
                           client_id=client_id, error=str(e))
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def join_room(self, client_id: str, room_id: str):
        """Add a client to a room"""
        if room_id not in self.room_connections:
            self.room_connections[room_id] = []
        
        if client_id not in self.room_connections[room_id]:
            self.room_connections[room_id].append(client_id)
            logger.info("Client joined room", client_id=client_id, room_id=room_id)
    
    def leave_room(self, client_id: str, room_id: str):
        """Remove a client from a room"""
        if room_id in self.room_connections:
            if client_id in self.room_connections[room_id]:
                self.room_connections[room_id].remove(client_id)
                logger.info("Client left room", client_id=client_id, room_id=room_id)
    
    async def send_to_room(self, message: str, room_id: str):
        """Send a message to all clients in a room"""
        if room_id not in self.room_connections:
            return
        
        disconnected_clients = []
        
        for client_id in self.room_connections[room_id]:
            if client_id in self.active_connections:
                try:
                    websocket = self.active_connections[client_id]
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error("Failed to send room message", 
                               client_id=client_id, room_id=room_id, error=str(e))
                    disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def send_json_to_room(self, data: dict, room_id: str):
        """Send a JSON message to all clients in a room"""
        if room_id not in self.room_connections:
            return
        
        disconnected_clients = []
        
        for client_id in self.room_connections[room_id]:
            if client_id in self.active_connections:
                try:
                    websocket = self.active_connections[client_id]
                    await websocket.send_json(data)
                except Exception as e:
                    logger.error("Failed to send room JSON", 
                               client_id=client_id, room_id=room_id, error=str(e))
                    disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.active_connections)
    
    def get_room_count(self, room_id: str) -> int:
        """Get the number of clients in a room"""
        if room_id not in self.room_connections:
            return 0
        return len(self.room_connections[room_id])
