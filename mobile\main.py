"""
Main entry point for the Warehouse Inventory Management Mobile Application
Built with <PERSON><PERSON> for cross-platform mobile deployment
"""
import os
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.clock import Clock
from kivy.logger import Logger
from kivy.config import Config

# Configure Kivy before importing other modules
Config.set('graphics', 'width', '360')
Config.set('graphics', 'height', '640')
Config.set('graphics', 'resizable', False)

from kivymd.app import MDApp
from kivymd.theming import ThemableBehavior
from kivymd.uix.screen import MDScreen
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.navigationdrawer import MDNavigationDrawer, MDNavigationDrawerMenu
from kivymd.uix.list import MDList, OneLineListItem

from src.core.config import MobileConfig
from src.core.logger import setup_mobile_logging
from src.screens.login_screen import LoginScreen
from src.screens.dashboard_screen import DashboardScreen
from src.screens.inventory_screen import InventoryScreen
from src.screens.scanner_screen import ScannerScreen
from src.screens.orders_screen import OrdersScreen
from src.screens.settings_screen import SettingsScreen
from src.services.api_client import MobileAPIClient
from src.services.offline_manager import OfflineManager
from src.core.database import init_mobile_db

# Setup logging
logger = setup_mobile_logging()


class NavigationDrawer(MDNavigationDrawer):
    """Custom navigation drawer for the app"""
    
    def __init__(self, app_instance, **kwargs):
        super().__init__(**kwargs)
        self.app_instance = app_instance
        self.setup_drawer()
    
    def setup_drawer(self):
        """Setup the navigation drawer content"""
        drawer_menu = MDNavigationDrawerMenu()
        
        # Menu items
        menu_items = [
            {"text": "Dashboard", "icon": "view-dashboard", "screen": "dashboard"},
            {"text": "Inventory", "icon": "package-variant", "screen": "inventory"},
            {"text": "Scanner", "icon": "barcode-scan", "screen": "scanner"},
            {"text": "Orders", "icon": "clipboard-list", "screen": "orders"},
            {"text": "Settings", "icon": "cog", "screen": "settings"},
            {"text": "Logout", "icon": "logout", "screen": "logout"},
        ]
        
        for item in menu_items:
            drawer_item = OneLineListItem(
                text=item["text"],
                on_release=lambda x, screen=item["screen"]: self.navigate_to(screen)
            )
            drawer_menu.add_widget(drawer_item)
        
        self.add_widget(drawer_menu)
    
    def navigate_to(self, screen_name):
        """Navigate to a specific screen"""
        if screen_name == "logout":
            self.app_instance.logout()
        else:
            self.app_instance.root.current = screen_name
        self.set_state("close")


class MainLayout(MDBoxLayout):
    """Main layout with navigation drawer and screen manager"""
    
    def __init__(self, app_instance, **kwargs):
        super().__init__(**kwargs)
        self.app_instance = app_instance
        self.orientation = "vertical"
        
        # Create toolbar
        self.toolbar = MDTopAppBar(
            title="Warehouse Inventory",
            left_action_items=[["menu", lambda x: self.nav_drawer.set_state("open")]],
            right_action_items=[["sync", lambda x: self.app_instance.sync_data()]],
        )
        self.add_widget(self.toolbar)
        
        # Create navigation drawer
        self.nav_drawer = NavigationDrawer(app_instance)
        
        # Create screen manager
        self.screen_manager = MDScreenManager()
        
        # Add screens
        self.add_screens()
        
        # Add screen manager to drawer
        self.nav_drawer.add_widget(self.screen_manager)
        self.add_widget(self.nav_drawer)
    
    def add_screens(self):
        """Add all screens to the screen manager"""
        screens = [
            ("login", LoginScreen(name="login")),
            ("dashboard", DashboardScreen(name="dashboard")),
            ("inventory", InventoryScreen(name="inventory")),
            ("scanner", ScannerScreen(name="scanner")),
            ("orders", OrdersScreen(name="orders")),
            ("settings", SettingsScreen(name="settings")),
        ]
        
        for screen_name, screen_instance in screens:
            screen_instance.app_instance = self.app_instance
            self.screen_manager.add_widget(screen_instance)


class WarehouseInventoryApp(MDApp):
    """Main application class"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.config = MobileConfig()
        self.api_client = None
        self.offline_manager = None
        self.current_user = None
        self.is_online = False
        
        # Set app properties
        self.title = "Warehouse Inventory"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
    
    def build(self):
        """Build the application UI"""
        logger.info("Building Warehouse Inventory Mobile App")
        
        # Initialize services
        self.init_services()
        
        # Create main layout
        self.main_layout = MainLayout(self)
        
        # Check for saved login
        self.check_saved_login()
        
        return self.main_layout
    
    def init_services(self):
        """Initialize application services"""
        try:
            # Initialize local database
            init_mobile_db()
            logger.info("Mobile database initialized")
            
            # Initialize API client
            self.api_client = MobileAPIClient(self.config.API_BASE_URL)
            
            # Initialize offline manager
            self.offline_manager = OfflineManager(self.config.get_full_db_path())
            
            # Check API connectivity
            self.check_connectivity()
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            self.show_error("Initialization Error", str(e))
    
    def check_connectivity(self):
        """Check API connectivity"""
        try:
            if self.api_client and self.api_client.health_check():
                self.is_online = True
                self.update_toolbar_status("Online")
                logger.info("API connection established")
            else:
                self.is_online = False
                self.update_toolbar_status("Offline")
                logger.warning("API connection failed - running offline")
        except Exception as e:
            self.is_online = False
            self.update_toolbar_status("Offline")
            logger.error(f"Connectivity check failed: {e}")
    
    def update_toolbar_status(self, status):
        """Update toolbar with connection status"""
        if hasattr(self, 'main_layout') and self.main_layout.toolbar:
            self.main_layout.toolbar.title = f"Warehouse Inventory ({status})"
    
    def check_saved_login(self):
        """Check for saved login credentials"""
        try:
            # TODO: Implement secure credential storage
            # For now, show login screen
            self.show_login_screen()
        except Exception as e:
            logger.error(f"Failed to check saved login: {e}")
            self.show_login_screen()
    
    def show_login_screen(self):
        """Show the login screen"""
        if hasattr(self, 'main_layout'):
            self.main_layout.screen_manager.current = "login"
    
    def login(self, username, password):
        """Handle user login"""
        try:
            if self.is_online and self.api_client:
                # Online login
                user_data = self.api_client.login(username, password)
                if user_data:
                    self.current_user = user_data
                    self.on_login_success()
                    return True
                else:
                    self.show_error("Login Failed", "Invalid credentials")
                    return False
            else:
                # Offline login (check cached credentials)
                if self.offline_manager.verify_offline_login(username, password):
                    self.current_user = {"username": username, "offline": True}
                    self.on_login_success()
                    return True
                else:
                    self.show_error("Login Failed", "Cannot login offline - no cached credentials")
                    return False
        except Exception as e:
            logger.error(f"Login failed: {e}")
            self.show_error("Login Error", str(e))
            return False
    
    def logout(self):
        """Handle user logout"""
        try:
            self.current_user = None
            self.show_login_screen()
            logger.info("User logged out")
        except Exception as e:
            logger.error(f"Logout failed: {e}")
    
    def on_login_success(self):
        """Handle successful login"""
        logger.info(f"User logged in: {self.current_user.get('username')}")
        
        # Navigate to dashboard
        self.main_layout.screen_manager.current = "dashboard"
        
        # Start periodic sync if online
        if self.is_online:
            Clock.schedule_interval(self.auto_sync, self.config.SYNC_INTERVAL_MINUTES * 60)
    
    def sync_data(self):
        """Sync data with server"""
        if not self.is_online or not self.current_user:
            self.show_info("Sync", "Cannot sync - offline or not logged in")
            return
        
        try:
            logger.info("Starting data sync")
            # TODO: Implement actual sync logic
            self.show_info("Sync", "Data synchronized successfully")
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            self.show_error("Sync Error", str(e))
    
    def auto_sync(self, dt):
        """Automatic sync callback"""
        if self.is_online and self.current_user:
            self.sync_data()
    
    def show_error(self, title, message):
        """Show error dialog"""
        # TODO: Implement proper error dialog
        logger.error(f"{title}: {message}")
    
    def show_info(self, title, message):
        """Show info dialog"""
        # TODO: Implement proper info dialog
        logger.info(f"{title}: {message}")
    
    def on_start(self):
        """Called when the app starts"""
        logger.info("Warehouse Inventory Mobile App started")
    
    def on_stop(self):
        """Called when the app stops"""
        logger.info("Warehouse Inventory Mobile App stopped")
    
    def on_pause(self):
        """Called when the app is paused"""
        logger.info("App paused")
        return True
    
    def on_resume(self):
        """Called when the app resumes"""
        logger.info("App resumed")
        # Check connectivity when resuming
        self.check_connectivity()


if __name__ == "__main__":
    try:
        app = WarehouseInventoryApp()
        app.run()
    except Exception as e:
        Logger.error(f"Failed to start application: {e}")
        sys.exit(1)
