"""
Inventory models for items, categories, and stock movements
"""
from sqlalchemy import (
    Column, Integer, String, Text, Decimal, Boolean, DateTime, 
    ForeignKey, Enum, Index
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from decimal import Decimal as PyDecimal

from app.core.database import Base


class Category(Base):
    """Product categories for organization"""
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    
    # Hierarchy
    parent = relationship("Category", remote_side=[id], backref="children")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Items in this category
    items = relationship("Item", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}')>"


class Item(Base):
    """Inventory items/products"""
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    sku = Column(String(50), unique=True, index=True, nullable=False)
    barcode = Column(String(50), unique=True, index=True, nullable=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Category
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    category = relationship("Category", back_populates="items")
    
    # Pricing
    cost_price = Column(Decimal(10, 2), nullable=True)
    selling_price = Column(Decimal(10, 2), nullable=True)
    
    # Physical properties
    weight = Column(Decimal(8, 3), nullable=True)  # in kg
    dimensions = Column(String(50), nullable=True)  # LxWxH in cm
    
    # Stock information
    current_stock = Column(Integer, default=0, nullable=False)
    reserved_stock = Column(Integer, default=0, nullable=False)
    available_stock = Column(Integer, default=0, nullable=False)
    
    # Stock management
    min_stock_level = Column(Integer, default=0, nullable=False)
    max_stock_level = Column(Integer, nullable=True)
    reorder_point = Column(Integer, default=0, nullable=False)
    reorder_quantity = Column(Integer, default=0, nullable=False)
    
    # Supplier information
    primary_supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True)
    primary_supplier = relationship("Supplier", foreign_keys=[primary_supplier_id])
    
    # Status and flags
    is_active = Column(Boolean, default=True, nullable=False)
    is_serialized = Column(Boolean, default=False, nullable=False)
    is_batch_tracked = Column(Boolean, default=False, nullable=False)
    is_perishable = Column(Boolean, default=False, nullable=False)
    
    # Location
    default_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    default_location = relationship("Location")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_counted = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    stock_movements = relationship("StockMovement", back_populates="item")
    order_items = relationship("OrderItem", back_populates="item")
    
    # Indexes for performance
    __table_args__ = (
        Index('ix_items_current_stock', 'current_stock'),
        Index('ix_items_min_stock', 'min_stock_level'),
        Index('ix_items_active', 'is_active'),
    )
    
    def __repr__(self):
        return f"<Item(id={self.id}, sku='{self.sku}', name='{self.name}')>"
    
    @property
    def is_low_stock(self) -> bool:
        """Check if item is below minimum stock level"""
        return self.available_stock <= self.min_stock_level
    
    @property
    def needs_reorder(self) -> bool:
        """Check if item needs to be reordered"""
        return self.available_stock <= self.reorder_point
    
    def update_available_stock(self):
        """Update available stock (current - reserved)"""
        self.available_stock = max(0, self.current_stock - self.reserved_stock)


class StockMovementType(enum.Enum):
    """Types of stock movements"""
    RECEIPT = "receipt"           # Receiving stock
    ISSUE = "issue"              # Issuing/shipping stock
    ADJUSTMENT = "adjustment"     # Stock adjustments
    TRANSFER = "transfer"        # Transfer between locations
    CYCLE_COUNT = "cycle_count"  # Cycle counting adjustments
    DAMAGE = "damage"            # Damaged goods
    RETURN = "return"            # Returns


class StockMovement(Base):
    """Stock movement transactions"""
    __tablename__ = "stock_movements"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Item reference
    item_id = Column(Integer, ForeignKey("items.id"), nullable=False)
    item = relationship("Item", back_populates="stock_movements")
    
    # Movement details
    movement_type = Column(Enum(StockMovementType), nullable=False)
    quantity = Column(Integer, nullable=False)
    unit_cost = Column(Decimal(10, 2), nullable=True)
    
    # Location information
    from_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    to_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    from_location = relationship("Location", foreign_keys=[from_location_id])
    to_location = relationship("Location", foreign_keys=[to_location_id])
    
    # Reference information
    reference_type = Column(String(50), nullable=True)  # order, adjustment, etc.
    reference_id = Column(Integer, nullable=True)
    reference_number = Column(String(50), nullable=True)
    
    # Batch/Serial tracking
    batch_number = Column(String(50), nullable=True)
    serial_number = Column(String(50), nullable=True)
    expiry_date = Column(DateTime(timezone=True), nullable=True)
    
    # User and notes
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User")
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    movement_date = Column(DateTime(timezone=True), default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('ix_stock_movements_item_date', 'item_id', 'movement_date'),
        Index('ix_stock_movements_type', 'movement_type'),
        Index('ix_stock_movements_reference', 'reference_type', 'reference_id'),
    )
    
    def __repr__(self):
        return f"<StockMovement(id={self.id}, item_id={self.item_id}, type='{self.movement_type.value}', qty={self.quantity})>"
