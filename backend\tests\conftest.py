"""
Pytest configuration and fixtures for testing
"""
import pytest
import asyncio
from typing import As<PERSON><PERSON>enerator, Generator
from decimal import Decima<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from httpx import AsyncClient
import jwt
from datetime import datetime, timedelta

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings
from app.models.user import User, UserRole
from app.models.inventory import Item, Category
from app.models.location import Warehouse, Zone, Location
from app.models.supplier import Supplier


# Test database URL (SQLite in memory)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


class TestDatabase:
    """Test database manager"""
    
    def __init__(self):
        self.engine = create_async_engine(
            TEST_DATABASE_URL,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=False
        )
        self.SessionLocal = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    
    async def create_tables(self):
        """Create all database tables"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    async def drop_tables(self):
        """Drop all database tables"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
    
    async def get_session(self) -> AsyncSession:
        """Get database session"""
        async with self.SessionLocal() as session:
            yield session


# Global test database instance
test_db_manager = TestDatabase()


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_test_db():
    """Setup test database"""
    await test_db_manager.create_tables()
    yield
    await test_db_manager.drop_tables()


@pytest.fixture
async def test_db(setup_test_db) -> AsyncGenerator[AsyncSession, None]:
    """Get test database session"""
    async with test_db_manager.SessionLocal() as session:
        yield session
        await session.rollback()


@pytest.fixture
async def override_get_db(test_db: AsyncSession):
    """Override the get_db dependency"""
    async def _override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


@pytest.fixture
async def client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """Get test HTTP client"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
async def test_user(test_db: AsyncSession) -> User:
    """Create a test user"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        role=UserRole.WAREHOUSE_STAFF,
        is_active=True,
        is_verified=True
    )
    
    test_db.add(user)
    await test_db.commit()
    await test_db.refresh(user)
    return user


@pytest.fixture
async def test_admin_user(test_db: AsyncSession) -> User:
    """Create a test admin user"""
    user = User(
        username="admin",
        email="<EMAIL>",
        full_name="Admin User",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "secret"
        role=UserRole.ADMIN,
        is_active=True,
        is_verified=True
    )
    
    test_db.add(user)
    await test_db.commit()
    await test_db.refresh(user)
    return user


@pytest.fixture
def auth_token(test_user: User) -> str:
    """Create authentication token for test user"""
    payload = {
        "sub": str(test_user.id),
        "username": test_user.username,
        "role": test_user.role.value,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return token


@pytest.fixture
def admin_auth_token(test_admin_user: User) -> str:
    """Create authentication token for admin user"""
    payload = {
        "sub": str(test_admin_user.id),
        "username": test_admin_user.username,
        "role": test_admin_user.role.value,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return token


@pytest.fixture
def auth_headers(auth_token: str) -> dict:
    """Create authentication headers"""
    return {"Authorization": f"Bearer {auth_token}"}


@pytest.fixture
def admin_auth_headers(admin_auth_token: str) -> dict:
    """Create admin authentication headers"""
    return {"Authorization": f"Bearer {admin_auth_token}"}


@pytest.fixture
async def test_category(test_db: AsyncSession) -> Category:
    """Create a test category"""
    category = Category(
        name="Test Category",
        description="A category for testing"
    )
    
    test_db.add(category)
    await test_db.commit()
    await test_db.refresh(category)
    return category


@pytest.fixture
async def test_item(test_db: AsyncSession, test_category: Category) -> Item:
    """Create a test inventory item"""
    item = Item(
        sku="TEST-ITEM-001",
        barcode="1234567890123",
        name="Test Item",
        description="A test inventory item",
        category_id=test_category.id,
        cost_price=Decimal("10.00"),
        selling_price=Decimal("15.00"),
        current_stock=100,
        available_stock=100,
        min_stock_level=10,
        reorder_point=20,
        reorder_quantity=50,
        is_active=True
    )
    
    test_db.add(item)
    await test_db.commit()
    await test_db.refresh(item)
    return item


@pytest.fixture
async def test_warehouse(test_db: AsyncSession) -> Warehouse:
    """Create a test warehouse"""
    warehouse = Warehouse(
        code="WH001",
        name="Test Warehouse",
        description="A warehouse for testing",
        address_line1="123 Test Street",
        city="Test City",
        state="Test State",
        postal_code="12345",
        country="Test Country",
        is_active=True
    )
    
    test_db.add(warehouse)
    await test_db.commit()
    await test_db.refresh(warehouse)
    return warehouse


@pytest.fixture
async def test_zone(test_db: AsyncSession, test_warehouse: Warehouse) -> Zone:
    """Create a test zone"""
    zone = Zone(
        code="A",
        name="Zone A",
        description="Test zone A",
        warehouse_id=test_warehouse.id,
        zone_type="storage",
        is_active=True
    )
    
    test_db.add(zone)
    await test_db.commit()
    await test_db.refresh(zone)
    return zone


@pytest.fixture
async def test_location(test_db: AsyncSession, test_warehouse: Warehouse, test_zone: Zone) -> Location:
    """Create a test location"""
    location = Location(
        code="01-01-01",
        name="Test Location",
        warehouse_id=test_warehouse.id,
        zone_id=test_zone.id,
        location_type="bin",
        is_active=True,
        is_pickable=True,
        is_receivable=True
    )
    
    test_db.add(location)
    await test_db.commit()
    await test_db.refresh(location)
    return location


@pytest.fixture
async def test_supplier(test_db: AsyncSession) -> Supplier:
    """Create a test supplier"""
    supplier = Supplier(
        code="SUP001",
        name="Test Supplier",
        legal_name="Test Supplier Ltd.",
        email="<EMAIL>",
        phone="+1234567890",
        address_line1="456 Supplier Street",
        city="Supplier City",
        state="Supplier State",
        postal_code="67890",
        country="Supplier Country",
        payment_terms="Net 30",
        currency="USD",
        lead_time_days=7,
        is_active=True,
        is_approved=True
    )
    
    test_db.add(supplier)
    await test_db.commit()
    await test_db.refresh(supplier)
    return supplier


@pytest.fixture
async def test_items_bulk(test_db: AsyncSession, test_category: Category) -> list[Item]:
    """Create multiple test items for bulk testing"""
    items = []
    
    for i in range(10):
        item = Item(
            sku=f"BULK-{i:03d}",
            name=f"Bulk Test Item {i}",
            description=f"Bulk test item number {i}",
            category_id=test_category.id,
            cost_price=Decimal(f"{10 + i}.00"),
            selling_price=Decimal(f"{15 + i}.00"),
            current_stock=100 - (i * 5),
            available_stock=100 - (i * 5),
            min_stock_level=10,
            reorder_point=20,
            reorder_quantity=50,
            is_active=True
        )
        items.append(item)
        test_db.add(item)
    
    await test_db.commit()
    
    for item in items:
        await test_db.refresh(item)
    
    return items


# Pytest configuration
def pytest_configure(config):
    """Configure pytest"""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
