"""
Automation service for inventory management
Handles automated reorder points, low stock alerts, demand forecasting, and order fulfillment
"""
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import asyncio
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.models.inventory import Item, StockMovement, StockMovementType
from app.models.order import Order, OrderItem, OrderType, OrderStatus
from app.models.supplier import Supplier
from app.models.user import User
from app.core.database import AsyncSessionLocal
from app.services.notification_service import NotificationService
from app.services.order_service import OrderService

logger = structlog.get_logger()


class AutomationService:
    """Service for inventory automation features"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.notification_service = NotificationService()
        self.order_service = OrderService(db)
    
    async def check_low_stock_items(self) -> List[Dict]:
        """Check for items below minimum stock level"""
        try:
            query = select(Item).where(
                and_(
                    Item.is_active == True,
                    Item.available_stock <= Item.min_stock_level,
                    Item.min_stock_level > 0
                )
            ).options(
                selectinload(Item.category),
                selectinload(Item.primary_supplier)
            )
            
            result = await self.db.execute(query)
            low_stock_items = result.scalars().all()
            
            low_stock_data = []
            for item in low_stock_items:
                low_stock_data.append({
                    'item_id': item.id,
                    'sku': item.sku,
                    'name': item.name,
                    'current_stock': item.current_stock,
                    'available_stock': item.available_stock,
                    'min_stock_level': item.min_stock_level,
                    'reorder_point': item.reorder_point,
                    'reorder_quantity': item.reorder_quantity,
                    'category': item.category.name if item.category else None,
                    'supplier': item.primary_supplier.name if item.primary_supplier else None,
                    'days_below_minimum': self._calculate_days_below_minimum(item)
                })
            
            logger.info(f"Found {len(low_stock_items)} low stock items")
            return low_stock_data
            
        except Exception as e:
            logger.error(f"Error checking low stock items: {e}")
            return []
    
    async def check_reorder_points(self) -> List[Dict]:
        """Check for items that need reordering"""
        try:
            query = select(Item).where(
                and_(
                    Item.is_active == True,
                    Item.available_stock <= Item.reorder_point,
                    Item.reorder_point > 0,
                    Item.reorder_quantity > 0
                )
            ).options(
                selectinload(Item.category),
                selectinload(Item.primary_supplier)
            )
            
            result = await self.db.execute(query)
            reorder_items = result.scalars().all()
            
            reorder_data = []
            for item in reorder_items:
                # Check if there's already a pending order for this item
                pending_order_qty = await self._get_pending_order_quantity(item.id)
                
                if pending_order_qty < item.reorder_quantity:
                    suggested_qty = item.reorder_quantity - pending_order_qty
                    
                    reorder_data.append({
                        'item_id': item.id,
                        'sku': item.sku,
                        'name': item.name,
                        'current_stock': item.current_stock,
                        'available_stock': item.available_stock,
                        'reorder_point': item.reorder_point,
                        'reorder_quantity': item.reorder_quantity,
                        'suggested_quantity': suggested_qty,
                        'pending_orders': pending_order_qty,
                        'supplier_id': item.primary_supplier_id,
                        'supplier_name': item.primary_supplier.name if item.primary_supplier else None,
                        'estimated_cost': self._calculate_estimated_cost(item, suggested_qty),
                        'lead_time_days': item.primary_supplier.lead_time_days if item.primary_supplier else 7
                    })
            
            logger.info(f"Found {len(reorder_data)} items needing reorder")
            return reorder_data
            
        except Exception as e:
            logger.error(f"Error checking reorder points: {e}")
            return []
    
    async def create_automatic_purchase_orders(self, user_id: int) -> List[Dict]:
        """Create automatic purchase orders for items needing reorder"""
        try:
            reorder_items = await self.check_reorder_points()
            
            if not reorder_items:
                return []
            
            # Group items by supplier
            supplier_groups = {}
            for item in reorder_items:
                supplier_id = item['supplier_id']
                if supplier_id:
                    if supplier_id not in supplier_groups:
                        supplier_groups[supplier_id] = []
                    supplier_groups[supplier_id].append(item)
            
            created_orders = []
            
            for supplier_id, items in supplier_groups.items():
                try:
                    # Create purchase order for this supplier
                    order_data = {
                        'order_type': OrderType.PURCHASE,
                        'supplier_id': supplier_id,
                        'description': f'Automatic reorder - {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                        'notes': 'Automatically generated purchase order based on reorder points',
                        'priority': 'normal',
                        'items': []
                    }
                    
                    total_estimated_cost = Decimal('0')
                    
                    for item in items:
                        order_data['items'].append({
                            'item_id': item['item_id'],
                            'quantity_ordered': item['suggested_quantity'],
                            'unit_price': item['estimated_cost'] / item['suggested_quantity'] if item['suggested_quantity'] > 0 else Decimal('0'),
                            'notes': f'Reorder point: {item["reorder_point"]}, Current stock: {item["current_stock"]}'
                        })
                        total_estimated_cost += item['estimated_cost']
                    
                    # Create the order
                    order = await self.order_service.create_purchase_order(order_data, user_id)
                    
                    created_orders.append({
                        'order_id': order.id,
                        'order_number': order.order_number,
                        'supplier_id': supplier_id,
                        'supplier_name': items[0]['supplier_name'],
                        'item_count': len(items),
                        'total_estimated_cost': total_estimated_cost,
                        'items': [item['sku'] for item in items]
                    })
                    
                    logger.info(f"Created automatic purchase order {order.order_number} for supplier {supplier_id}")
                    
                except Exception as e:
                    logger.error(f"Failed to create purchase order for supplier {supplier_id}: {e}")
                    continue
            
            return created_orders
            
        except Exception as e:
            logger.error(f"Error creating automatic purchase orders: {e}")
            return []
    
    async def calculate_demand_forecast(self, item_id: int, days: int = 30) -> Dict:
        """Calculate demand forecast for an item based on historical data"""
        try:
            # Get historical stock movements for the item
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days * 3)  # Look at 3x the forecast period
            
            query = select(StockMovement).where(
                and_(
                    StockMovement.item_id == item_id,
                    StockMovement.movement_type.in_([
                        StockMovementType.ISSUE,
                        StockMovementType.RECEIPT
                    ]),
                    StockMovement.movement_date >= start_date,
                    StockMovement.movement_date <= end_date
                )
            ).order_by(StockMovement.movement_date)
            
            result = await self.db.execute(query)
            movements = result.scalars().all()
            
            if not movements:
                return {
                    'item_id': item_id,
                    'forecast_days': days,
                    'predicted_demand': 0,
                    'confidence': 'low',
                    'trend': 'stable',
                    'seasonal_factor': 1.0,
                    'recommendation': 'Insufficient data for forecast'
                }
            
            # Calculate daily demand
            daily_issues = {}
            total_issues = 0
            
            for movement in movements:
                if movement.movement_type == StockMovementType.ISSUE:
                    date_key = movement.movement_date.date()
                    if date_key not in daily_issues:
                        daily_issues[date_key] = 0
                    daily_issues[date_key] += abs(movement.quantity)
                    total_issues += abs(movement.quantity)
            
            # Calculate average daily demand
            data_days = len(daily_issues)
            if data_days == 0:
                avg_daily_demand = 0
            else:
                avg_daily_demand = total_issues / data_days
            
            # Calculate trend (simple linear regression on recent data)
            trend = self._calculate_trend(daily_issues)
            
            # Apply seasonal factor (simplified)
            seasonal_factor = self._calculate_seasonal_factor(daily_issues)
            
            # Predict demand for the forecast period
            base_demand = avg_daily_demand * days
            trend_adjustment = trend * days * (days / 2)  # Compound trend effect
            seasonal_demand = base_demand * seasonal_factor
            
            predicted_demand = max(0, int(seasonal_demand + trend_adjustment))
            
            # Determine confidence level
            confidence = self._determine_confidence(data_days, movements)
            
            # Generate recommendation
            recommendation = self._generate_forecast_recommendation(
                predicted_demand, avg_daily_demand, trend, confidence
            )
            
            return {
                'item_id': item_id,
                'forecast_days': days,
                'predicted_demand': predicted_demand,
                'average_daily_demand': round(avg_daily_demand, 2),
                'confidence': confidence,
                'trend': 'increasing' if trend > 0.1 else 'decreasing' if trend < -0.1 else 'stable',
                'seasonal_factor': round(seasonal_factor, 2),
                'data_points': data_days,
                'historical_period_days': (end_date - start_date).days,
                'recommendation': recommendation
            }
            
        except Exception as e:
            logger.error(f"Error calculating demand forecast for item {item_id}: {e}")
            return {
                'item_id': item_id,
                'forecast_days': days,
                'predicted_demand': 0,
                'confidence': 'error',
                'error': str(e)
            }
    
    async def send_low_stock_alerts(self) -> int:
        """Send alerts for low stock items"""
        try:
            low_stock_items = await self.check_low_stock_items()
            
            if not low_stock_items:
                return 0
            
            # Group alerts by severity
            critical_items = [item for item in low_stock_items if item['available_stock'] == 0]
            warning_items = [item for item in low_stock_items if item['available_stock'] > 0]
            
            alerts_sent = 0
            
            # Send critical alerts
            if critical_items:
                await self.notification_service.send_critical_alert(
                    title="Critical Stock Alert",
                    message=f"{len(critical_items)} items are out of stock",
                    data={'items': critical_items}
                )
                alerts_sent += 1
            
            # Send warning alerts
            if warning_items:
                await self.notification_service.send_warning_alert(
                    title="Low Stock Warning",
                    message=f"{len(warning_items)} items are below minimum stock level",
                    data={'items': warning_items}
                )
                alerts_sent += 1
            
            logger.info(f"Sent {alerts_sent} low stock alerts")
            return alerts_sent
            
        except Exception as e:
            logger.error(f"Error sending low stock alerts: {e}")
            return 0
    
    def _calculate_days_below_minimum(self, item: Item) -> int:
        """Calculate how many days an item has been below minimum stock"""
        # This would require tracking when the item first went below minimum
        # For now, return 0 as placeholder
        return 0
    
    async def _get_pending_order_quantity(self, item_id: int) -> int:
        """Get quantity of item in pending purchase orders"""
        try:
            query = select(func.sum(OrderItem.quantity_ordered - OrderItem.quantity_received)).select_from(
                OrderItem.__table__.join(Order.__table__)
            ).where(
                and_(
                    OrderItem.item_id == item_id,
                    Order.order_type == OrderType.PURCHASE,
                    Order.status.in_([OrderStatus.PENDING, OrderStatus.APPROVED, OrderStatus.PROCESSING])
                )
            )
            
            result = await self.db.execute(query)
            pending_qty = result.scalar() or 0
            return int(pending_qty)
            
        except Exception as e:
            logger.error(f"Error getting pending order quantity for item {item_id}: {e}")
            return 0
    
    def _calculate_estimated_cost(self, item: Item, quantity: int) -> Decimal:
        """Calculate estimated cost for reordering an item"""
        if item.cost_price:
            return item.cost_price * quantity
        elif item.selling_price:
            # Estimate cost as 70% of selling price
            return item.selling_price * Decimal('0.7') * quantity
        else:
            # Default estimate
            return Decimal('10.00') * quantity
    
    def _calculate_trend(self, daily_issues: Dict) -> float:
        """Calculate trend in demand (simplified linear regression)"""
        if len(daily_issues) < 7:
            return 0.0
        
        # Convert to list of (day_number, demand) pairs
        sorted_dates = sorted(daily_issues.keys())
        data_points = [(i, daily_issues[date]) for i, date in enumerate(sorted_dates)]
        
        # Simple linear regression
        n = len(data_points)
        sum_x = sum(x for x, y in data_points)
        sum_y = sum(y for x, y in data_points)
        sum_xy = sum(x * y for x, y in data_points)
        sum_x2 = sum(x * x for x, y in data_points)
        
        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope
    
    def _calculate_seasonal_factor(self, daily_issues: Dict) -> float:
        """Calculate seasonal adjustment factor (simplified)"""
        # For now, return 1.0 (no seasonal adjustment)
        # In a real implementation, this would analyze seasonal patterns
        return 1.0
    
    def _determine_confidence(self, data_days: int, movements: List) -> str:
        """Determine confidence level of forecast"""
        if data_days < 7:
            return 'low'
        elif data_days < 30:
            return 'medium'
        else:
            return 'high'
    
    def _generate_forecast_recommendation(self, predicted_demand: int, avg_daily_demand: float, 
                                        trend: float, confidence: str) -> str:
        """Generate recommendation based on forecast"""
        if confidence == 'low':
            return "Insufficient historical data. Consider manual review."
        
        if predicted_demand == 0:
            return "No demand predicted. Consider reviewing item status."
        
        if trend > 0.5:
            return f"Increasing demand trend. Consider ordering {int(predicted_demand * 1.2)} units."
        elif trend < -0.5:
            return f"Decreasing demand trend. Consider ordering {int(predicted_demand * 0.8)} units."
        else:
            return f"Stable demand. Order {predicted_demand} units as predicted."
