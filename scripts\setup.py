#!/usr/bin/env python3
"""
Setup script for Warehouse Inventory Management System
This script helps set up the development environment and initialize the system
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path
import argparse


def run_command(command, cwd=None, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """Check if Python version is 3.11+"""
    if sys.version_info < (3, 11):
        print("Error: Python 3.11 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def check_dependencies():
    """Check if required system dependencies are installed"""
    dependencies = {
        'docker': 'Docker',
        'docker-compose': 'Docker Compose',
        'git': 'Git',
        'curl': 'cURL'
    }
    
    missing = []
    for cmd, name in dependencies.items():
        if shutil.which(cmd) is None:
            missing.append(name)
        else:
            print(f"✓ {name} found")
    
    if missing:
        print(f"Error: Missing dependencies: {', '.join(missing)}")
        print("Please install the missing dependencies and try again")
        sys.exit(1)


def setup_environment():
    """Setup environment files"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✓ Created .env file from .env.example")
        print("⚠️  Please edit .env file with your configuration")
    else:
        print("✓ .env file already exists")


def setup_backend():
    """Setup backend environment"""
    print("\n=== Setting up Backend ===")
    backend_dir = Path('backend')
    
    if not backend_dir.exists():
        print("Error: Backend directory not found")
        return False
    
    # Create virtual environment
    venv_path = backend_dir / 'venv'
    if not venv_path.exists():
        run_command(f"python -m venv {venv_path}", cwd=backend_dir)
        print("✓ Created virtual environment")
    
    # Install dependencies
    if os.name == 'nt':  # Windows
        pip_cmd = str(venv_path / 'Scripts' / 'pip')
    else:  # Linux/Mac
        pip_cmd = str(venv_path / 'bin' / 'pip')
    
    run_command(f"{pip_cmd} install --upgrade pip", cwd=backend_dir)
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir)
    print("✓ Installed backend dependencies")
    
    return True


def setup_desktop():
    """Setup desktop application environment"""
    print("\n=== Setting up Desktop Application ===")
    desktop_dir = Path('desktop')
    
    if not desktop_dir.exists():
        print("Error: Desktop directory not found")
        return False
    
    # Create virtual environment
    venv_path = desktop_dir / 'venv'
    if not venv_path.exists():
        run_command(f"python -m venv {venv_path}", cwd=desktop_dir)
        print("✓ Created virtual environment")
    
    # Install dependencies
    if os.name == 'nt':  # Windows
        pip_cmd = str(venv_path / 'Scripts' / 'pip')
    else:  # Linux/Mac
        pip_cmd = str(venv_path / 'bin' / 'pip')
    
    run_command(f"{pip_cmd} install --upgrade pip", cwd=desktop_dir)
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=desktop_dir)
    print("✓ Installed desktop dependencies")
    
    return True


def setup_mobile():
    """Setup mobile application environment"""
    print("\n=== Setting up Mobile Application ===")
    mobile_dir = Path('mobile')
    
    if not mobile_dir.exists():
        print("Error: Mobile directory not found")
        return False
    
    # Create virtual environment
    venv_path = mobile_dir / 'venv'
    if not venv_path.exists():
        run_command(f"python -m venv {venv_path}", cwd=mobile_dir)
        print("✓ Created virtual environment")
    
    # Install dependencies
    if os.name == 'nt':  # Windows
        pip_cmd = str(venv_path / 'Scripts' / 'pip')
    else:  # Linux/Mac
        pip_cmd = str(venv_path / 'bin' / 'pip')
    
    run_command(f"{pip_cmd} install --upgrade pip", cwd=mobile_dir)
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=mobile_dir)
    print("✓ Installed mobile dependencies")
    
    return True


def setup_docker():
    """Setup Docker environment"""
    print("\n=== Setting up Docker Environment ===")
    
    # Build and start services
    run_command("docker-compose build")
    print("✓ Built Docker images")
    
    run_command("docker-compose up -d postgres redis")
    print("✓ Started database services")
    
    # Wait for services to be ready
    print("Waiting for services to be ready...")
    import time
    time.sleep(10)
    
    # Run database migrations
    run_command("docker-compose exec backend alembic upgrade head", check=False)
    print("✓ Applied database migrations")
    
    return True


def create_directories():
    """Create necessary directories"""
    directories = [
        'backend/uploads',
        'backend/logs',
        'desktop/data',
        'desktop/logs',
        'mobile/data',
        'mobile/logs',
        'docs',
        'scripts',
        'monitoring'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ Created necessary directories")


def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description='Setup Warehouse Inventory Management System')
    parser.add_argument('--backend-only', action='store_true', help='Setup backend only')
    parser.add_argument('--desktop-only', action='store_true', help='Setup desktop only')
    parser.add_argument('--mobile-only', action='store_true', help='Setup mobile only')
    parser.add_argument('--docker-only', action='store_true', help='Setup Docker only')
    parser.add_argument('--no-docker', action='store_true', help='Skip Docker setup')
    
    args = parser.parse_args()
    
    print("🏭 Warehouse Inventory Management System Setup")
    print("=" * 50)
    
    # Check prerequisites
    check_python_version()
    check_dependencies()
    
    # Setup environment
    setup_environment()
    create_directories()
    
    success = True
    
    # Setup components based on arguments
    if args.backend_only:
        success &= setup_backend()
    elif args.desktop_only:
        success &= setup_desktop()
    elif args.mobile_only:
        success &= setup_mobile()
    elif args.docker_only:
        success &= setup_docker()
    else:
        # Setup all components
        success &= setup_backend()
        success &= setup_desktop()
        success &= setup_mobile()
        
        if not args.no_docker:
            success &= setup_docker()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your configuration")
        print("2. Start the backend: docker-compose up -d")
        print("3. Run desktop app: cd desktop && python main.py")
        print("4. Run mobile app: cd mobile && python main.py")
        print("5. Access API docs: http://localhost:8000/docs")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
