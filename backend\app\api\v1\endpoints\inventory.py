"""
Inventory management API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import structlog

from app.core.database import get_db
from app.models.inventory import Item, Category, StockMovement
from app.schemas.inventory import (
    ItemCreate, ItemUpdate, ItemResponse, ItemList,
    CategoryCreate, CategoryUpdate, CategoryResponse,
    StockMovementCreate, StockMovementResponse
)
from app.services.inventory_service import InventoryService
from app.core.auth import get_current_user
from app.models.user import User

logger = structlog.get_logger()
router = APIRouter()


@router.get("/items", response_model=ItemList)
async def get_items(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[int] = Query(None),
    low_stock_only: bool = Query(False),
    active_only: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of inventory items with filtering and pagination"""
    try:
        inventory_service = InventoryService(db)
        items, total = await inventory_service.get_items(
            skip=skip,
            limit=limit,
            search=search,
            category_id=category_id,
            low_stock_only=low_stock_only,
            active_only=active_only
        )
        
        return ItemList(
            items=items,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error("Failed to get items", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve items"
        )


@router.get("/items/{item_id}", response_model=ItemResponse)
async def get_item(
    item_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific inventory item by ID"""
    try:
        inventory_service = InventoryService(db)
        item = await inventory_service.get_item_by_id(item_id)
        
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found"
            )
        
        return item
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get item", error=str(e), item_id=item_id, user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve item"
        )


@router.post("/items", response_model=ItemResponse, status_code=status.HTTP_201_CREATED)
async def create_item(
    item_data: ItemCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new inventory item"""
    try:
        # Check permissions
        if not current_user.can_manage_inventory:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to create items"
            )
        
        inventory_service = InventoryService(db)
        
        # Check if SKU already exists
        existing_item = await inventory_service.get_item_by_sku(item_data.sku)
        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Item with this SKU already exists"
            )
        
        # Check if barcode already exists (if provided)
        if item_data.barcode:
            existing_barcode = await inventory_service.get_item_by_barcode(item_data.barcode)
            if existing_barcode:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Item with this barcode already exists"
                )
        
        item = await inventory_service.create_item(item_data, current_user.id)
        logger.info("Item created", item_id=item.id, sku=item.sku, user_id=current_user.id)
        
        return item
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create item", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create item"
        )


@router.put("/items/{item_id}", response_model=ItemResponse)
async def update_item(
    item_id: int,
    item_data: ItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update an existing inventory item"""
    try:
        # Check permissions
        if not current_user.can_manage_inventory:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to update items"
            )
        
        inventory_service = InventoryService(db)
        
        # Check if item exists
        existing_item = await inventory_service.get_item_by_id(item_id)
        if not existing_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found"
            )
        
        item = await inventory_service.update_item(item_id, item_data, current_user.id)
        logger.info("Item updated", item_id=item.id, sku=item.sku, user_id=current_user.id)
        
        return item
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update item", error=str(e), item_id=item_id, user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update item"
        )


@router.delete("/items/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_item(
    item_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an inventory item (soft delete)"""
    try:
        # Check permissions
        if not current_user.is_manager:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to delete items"
            )
        
        inventory_service = InventoryService(db)
        
        # Check if item exists
        existing_item = await inventory_service.get_item_by_id(item_id)
        if not existing_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found"
            )
        
        await inventory_service.delete_item(item_id, current_user.id)
        logger.info("Item deleted", item_id=item_id, user_id=current_user.id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete item", error=str(e), item_id=item_id, user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete item"
        )


@router.post("/items/{item_id}/stock-movement", response_model=StockMovementResponse)
async def create_stock_movement(
    item_id: int,
    movement_data: StockMovementCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a stock movement for an item"""
    try:
        # Check permissions
        if not current_user.can_manage_inventory:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to create stock movements"
            )
        
        inventory_service = InventoryService(db)
        
        # Check if item exists
        item = await inventory_service.get_item_by_id(item_id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found"
            )
        
        movement = await inventory_service.create_stock_movement(
            item_id, movement_data, current_user.id
        )
        
        logger.info(
            "Stock movement created",
            movement_id=movement.id,
            item_id=item_id,
            movement_type=movement_data.movement_type,
            quantity=movement_data.quantity,
            user_id=current_user.id
        )
        
        return movement
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create stock movement", error=str(e), item_id=item_id, user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create stock movement"
        )


@router.get("/items/{item_id}/stock-movements", response_model=List[StockMovementResponse])
async def get_item_stock_movements(
    item_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get stock movements for a specific item"""
    try:
        inventory_service = InventoryService(db)
        
        # Check if item exists
        item = await inventory_service.get_item_by_id(item_id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found"
            )
        
        movements = await inventory_service.get_item_stock_movements(
            item_id, skip=skip, limit=limit
        )
        
        return movements
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get stock movements", error=str(e), item_id=item_id, user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve stock movements"
        )


@router.get("/low-stock", response_model=List[ItemResponse])
async def get_low_stock_items(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get items that are below minimum stock level"""
    try:
        inventory_service = InventoryService(db)
        items = await inventory_service.get_low_stock_items()
        
        return items
    except Exception as e:
        logger.error("Failed to get low stock items", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve low stock items"
        )
