# 🔧 Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 1. Python Version Issues

#### Problem: "Python 3.11+ required"
```bash
# Check your Python version
python --version
# or
python3 --version
```

**Solutions:**
- **Windows**: Download Python 3.11+ from [python.org](https://python.org)
- **Mac**: Use Homebrew: `brew install python@3.11`
- **Linux**: Use package manager: `sudo apt install python3.11`

#### Problem: "python command not found"
**Solutions:**
- **Windows**: Add Python to PATH during installation
- **Mac/Linux**: Use `python3` instead of `python`
- Create alias: `alias python=python3`

### 2. Docker Issues

#### Problem: "Docker not found"
**Solutions:**
1. Install Docker Desktop from [docker.com](https://docker.com)
2. Start Docker Desktop application
3. Verify: `docker --version`

#### Problem: "Port already in use"
```bash
# Find process using port 8000
netstat -ano | findstr :8000  # Windows
lsof -ti:8000                 # Mac/Linux

# Kill the process
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Mac/Linux
```

#### Problem: "Docker containers won't start"
```bash
# Reset Docker environment
docker-compose down
docker system prune -f
docker-compose up -d
```

### 3. Database Connection Issues

#### Problem: "Database connection failed"
**Solutions:**
1. Check if PostgreSQL container is running:
   ```bash
   docker-compose ps
   ```

2. Restart database:
   ```bash
   docker-compose restart postgres
   ```

3. Check database logs:
   ```bash
   docker-compose logs postgres
   ```

4. Reset database:
   ```bash
   docker-compose down
   docker volume rm warehouse_postgres_data
   docker-compose up -d postgres
   ```

### 4. Package Installation Issues

#### Problem: "pip install failed"
**Solutions:**
1. Upgrade pip:
   ```bash
   python -m pip install --upgrade pip
   ```

2. Clear pip cache:
   ```bash
   pip cache purge
   ```

3. Install with no cache:
   ```bash
   pip install --no-cache-dir -r requirements.txt
   ```

4. Use different index:
   ```bash
   pip install -i https://pypi.org/simple/ -r requirements.txt
   ```

#### Problem: "Virtual environment issues"
**Solutions:**
1. Delete and recreate venv:
   ```bash
   rm -rf venv  # or rmdir /s venv on Windows
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate
   pip install -r requirements.txt
   ```

### 5. Application Startup Issues

#### Problem: "Desktop app won't start"
**Solutions:**
1. Check PyQt6 installation:
   ```bash
   pip install PyQt6
   ```

2. Install system dependencies:
   ```bash
   # Ubuntu/Debian
   sudo apt install python3-pyqt6
   
   # Mac
   brew install pyqt6
   ```

3. Check display settings (Linux):
   ```bash
   export DISPLAY=:0
   ```

#### Problem: "Mobile app crashes"
**Solutions:**
1. Install Kivy dependencies:
   ```bash
   pip install kivy[base]
   ```

2. Check OpenGL support:
   ```bash
   # Install OpenGL libraries
   pip install PyOpenGL PyOpenGL_accelerate
   ```

### 6. Camera/Barcode Scanner Issues

#### Problem: "Camera not detected"
**Solutions:**
1. Check camera permissions in system settings
2. Install OpenCV:
   ```bash
   pip install opencv-python
   ```

3. Test camera:
   ```python
   import cv2
   cap = cv2.VideoCapture(0)
   print(cap.isOpened())
   ```

#### Problem: "Barcode scanning not working"
**Solutions:**
1. Install pyzbar:
   ```bash
   pip install pyzbar
   ```

2. Install system dependencies:
   ```bash
   # Ubuntu/Debian
   sudo apt install libzbar0
   
   # Mac
   brew install zbar
   
   # Windows - usually works out of the box
   ```

### 7. Network and API Issues

#### Problem: "API not responding"
**Solutions:**
1. Check if backend is running:
   ```bash
   curl http://localhost:8000/health
   ```

2. Check backend logs:
   ```bash
   docker-compose logs backend
   ```

3. Restart backend:
   ```bash
   docker-compose restart backend
   ```

#### Problem: "CORS errors in browser"
**Solutions:**
1. Check ALLOWED_HOSTS in .env file
2. Add your domain to CORS settings
3. Restart backend after changes

### 8. Performance Issues

#### Problem: "System running slowly"
**Solutions:**
1. Check system resources:
   ```bash
   docker stats
   ```

2. Increase Docker memory allocation
3. Close unnecessary applications
4. Check disk space

#### Problem: "Database queries slow"
**Solutions:**
1. Check database indexes
2. Optimize queries
3. Increase PostgreSQL memory settings

### 9. File Permission Issues (Linux/Mac)

#### Problem: "Permission denied"
**Solutions:**
1. Make scripts executable:
   ```bash
   chmod +x scripts/*.py
   chmod +x *.sh
   ```

2. Fix ownership:
   ```bash
   sudo chown -R $USER:$USER .
   ```

### 10. Environment Variable Issues

#### Problem: "Configuration not loading"
**Solutions:**
1. Check .env file exists:
   ```bash
   ls -la .env
   ```

2. Copy from example:
   ```bash
   cp .env.example .env
   ```

3. Check file format (no spaces around =):
   ```
   DATABASE_URL=postgresql://...
   # Not: DATABASE_URL = postgresql://...
   ```

## 🔍 Diagnostic Commands

### Check System Status
```bash
# Overall system status
python scripts/run.py status

# Docker services
docker-compose ps

# Check ports
netstat -tulpn | grep :8000  # Linux
netstat -ano | findstr :8000  # Windows
```

### Check Logs
```bash
# Application logs
tail -f backend/logs/app.log
tail -f desktop/logs/app.log
tail -f mobile/logs/app.log

# Docker logs
docker-compose logs -f backend
docker-compose logs -f postgres
docker-compose logs -f redis
```

### Test Components
```bash
# Test backend API
curl http://localhost:8000/health

# Test database connection
docker-compose exec postgres pg_isready -U warehouse

# Test Redis
docker-compose exec redis redis-cli ping

# Run application tests
python scripts/run.py test
```

## 🆘 Getting Help

### Before Asking for Help
1. ✅ Check this troubleshooting guide
2. ✅ Look at error messages carefully
3. ✅ Check system logs
4. ✅ Try restarting services
5. ✅ Verify prerequisites are met

### Information to Provide
When reporting issues, include:
- **Operating System**: Windows/Mac/Linux version
- **Python Version**: `python --version`
- **Error Message**: Full error text
- **Steps to Reproduce**: What you were doing
- **Log Files**: Relevant log entries
- **System Info**: RAM, disk space, etc.

### Reset Everything
If all else fails, complete reset:
```bash
# Stop all services
docker-compose down

# Remove all data
docker system prune -a -f
docker volume prune -f

# Remove virtual environments
rm -rf backend/venv desktop/venv mobile/venv

# Start fresh
python quick_start.py
```

## 📞 Support Resources

- **Setup Guide**: See SETUP_GUIDE.md
- **Documentation**: Check README.md
- **API Docs**: http://localhost:8000/docs
- **System Status**: `python scripts/run.py status`

Remember: Most issues are related to prerequisites, permissions, or port conflicts. Check these first!
