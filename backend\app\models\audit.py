"""
Audit models for tracking changes and system events
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class AuditAction(enum.Enum):
    """Types of audit actions"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    EXPORT = "export"
    IMPORT = "import"
    PRINT = "print"
    VIEW = "view"
    APPROVE = "approve"
    REJECT = "reject"
    CANCEL = "cancel"
    RESTORE = "restore"


class AuditLog(Base):
    """Audit log for tracking all system changes and events"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Action details
    action = Column(Enum(AuditAction), nullable=False)
    table_name = Column(String(50), nullable=True)
    record_id = Column(Integer, nullable=True)
    
    # User information
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    user = relationship("User")
    username = Column(String(50), nullable=True)  # Snapshot in case user is deleted
    
    # Session information
    session_id = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Change details
    description = Column(Text, nullable=True)
    old_values = Column(JSON, nullable=True)  # JSON of old field values
    new_values = Column(JSON, nullable=True)  # JSON of new field values
    changed_fields = Column(JSON, nullable=True)  # List of changed field names
    
    # Additional context
    module = Column(String(50), nullable=True)  # inventory, orders, users, etc.
    category = Column(String(50), nullable=True)  # security, data, system, etc.
    severity = Column(String(20), default="info", nullable=False)  # info, warning, error, critical
    
    # Request information
    request_method = Column(String(10), nullable=True)  # GET, POST, PUT, DELETE
    request_url = Column(String(500), nullable=True)
    request_data = Column(JSON, nullable=True)
    
    # Response information
    response_status = Column(Integer, nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action.value}', user='{self.username}', table='{self.table_name}')>"
    
    @classmethod
    def create_log(cls, action: AuditAction, user_id: int = None, username: str = None,
                   table_name: str = None, record_id: int = None, description: str = None,
                   old_values: dict = None, new_values: dict = None, changed_fields: list = None,
                   module: str = None, category: str = None, severity: str = "info",
                   session_id: str = None, ip_address: str = None, user_agent: str = None,
                   request_method: str = None, request_url: str = None, request_data: dict = None,
                   response_status: int = None, response_time_ms: int = None):
        """Create a new audit log entry"""
        return cls(
            action=action,
            user_id=user_id,
            username=username,
            table_name=table_name,
            record_id=record_id,
            description=description,
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields,
            module=module,
            category=category,
            severity=severity,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_url=request_url,
            request_data=request_data,
            response_status=response_status,
            response_time_ms=response_time_ms
        )
