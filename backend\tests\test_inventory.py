"""
Test cases for inventory management functionality
"""
import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from httpx import AsyncClient

from app.main import app
from app.core.database import get_db
from app.models.inventory import Item, Category, StockMovement, StockMovementType
from app.models.user import User, UserRole
from app.services.inventory_service import InventoryService
from tests.conftest import TestDatabase


class TestInventoryModels:
    """Test inventory model functionality"""
    
    @pytest.mark.asyncio
    async def test_create_category(self, test_db: AsyncSession):
        """Test creating a category"""
        category = Category(
            name="Electronics",
            description="Electronic components and devices"
        )
        
        test_db.add(category)
        await test_db.commit()
        await test_db.refresh(category)
        
        assert category.id is not None
        assert category.name == "Electronics"
        assert category.description == "Electronic components and devices"
    
    @pytest.mark.asyncio
    async def test_create_item(self, test_db: AsyncSession):
        """Test creating an inventory item"""
        # Create category first
        category = Category(name="Test Category")
        test_db.add(category)
        await test_db.commit()
        await test_db.refresh(category)
        
        # Create item
        item = Item(
            sku="TEST-001",
            barcode="1234567890123",
            name="Test Item",
            description="A test inventory item",
            category_id=category.id,
            cost_price=Decimal("10.00"),
            selling_price=Decimal("15.00"),
            current_stock=100,
            min_stock_level=10,
            reorder_point=20,
            reorder_quantity=50
        )
        
        test_db.add(item)
        await test_db.commit()
        await test_db.refresh(item)
        
        assert item.id is not None
        assert item.sku == "TEST-001"
        assert item.name == "Test Item"
        assert item.current_stock == 100
        assert item.category_id == category.id
    
    @pytest.mark.asyncio
    async def test_item_low_stock_property(self, test_db: AsyncSession):
        """Test item low stock property"""
        item = Item(
            sku="LOW-STOCK-001",
            name="Low Stock Item",
            current_stock=5,
            available_stock=5,
            min_stock_level=10
        )
        
        assert item.is_low_stock == True
        
        item.available_stock = 15
        assert item.is_low_stock == False
    
    @pytest.mark.asyncio
    async def test_item_needs_reorder_property(self, test_db: AsyncSession):
        """Test item needs reorder property"""
        item = Item(
            sku="REORDER-001",
            name="Reorder Item",
            current_stock=15,
            available_stock=15,
            reorder_point=20
        )
        
        assert item.needs_reorder == True
        
        item.available_stock = 25
        assert item.needs_reorder == False
    
    @pytest.mark.asyncio
    async def test_stock_movement_creation(self, test_db: AsyncSession, test_user: User):
        """Test creating stock movements"""
        # Create item
        item = Item(
            sku="MOVE-001",
            name="Movement Test Item",
            current_stock=100
        )
        test_db.add(item)
        await test_db.commit()
        await test_db.refresh(item)
        
        # Create stock movement
        movement = StockMovement(
            item_id=item.id,
            movement_type=StockMovementType.ISSUE,
            quantity=-10,
            user_id=test_user.id,
            notes="Test stock issue"
        )
        
        test_db.add(movement)
        await test_db.commit()
        await test_db.refresh(movement)
        
        assert movement.id is not None
        assert movement.item_id == item.id
        assert movement.movement_type == StockMovementType.ISSUE
        assert movement.quantity == -10


class TestInventoryService:
    """Test inventory service functionality"""
    
    @pytest.mark.asyncio
    async def test_create_item_service(self, test_db: AsyncSession, test_user: User):
        """Test creating item through service"""
        service = InventoryService(test_db)
        
        item_data = {
            'sku': 'SERVICE-001',
            'name': 'Service Test Item',
            'description': 'Created through service',
            'cost_price': Decimal('20.00'),
            'selling_price': Decimal('30.00'),
            'current_stock': 50,
            'min_stock_level': 5,
            'reorder_point': 10,
            'reorder_quantity': 25
        }
        
        item = await service.create_item(item_data, test_user.id)
        
        assert item.id is not None
        assert item.sku == 'SERVICE-001'
        assert item.name == 'Service Test Item'
        assert item.current_stock == 50
    
    @pytest.mark.asyncio
    async def test_get_items_with_pagination(self, test_db: AsyncSession):
        """Test getting items with pagination"""
        service = InventoryService(test_db)
        
        # Create test items
        for i in range(25):
            item = Item(
                sku=f'PAGE-{i:03d}',
                name=f'Pagination Test Item {i}',
                current_stock=10
            )
            test_db.add(item)
        
        await test_db.commit()
        
        # Test pagination
        items, total = await service.get_items(skip=0, limit=10)
        assert len(items) == 10
        assert total >= 25
        
        items, total = await service.get_items(skip=10, limit=10)
        assert len(items) == 10
    
    @pytest.mark.asyncio
    async def test_search_items(self, test_db: AsyncSession):
        """Test searching items"""
        service = InventoryService(test_db)
        
        # Create test items
        items_data = [
            {'sku': 'SEARCH-001', 'name': 'Laptop Computer'},
            {'sku': 'SEARCH-002', 'name': 'Desktop Computer'},
            {'sku': 'SEARCH-003', 'name': 'Mobile Phone'},
        ]
        
        for item_data in items_data:
            item = Item(**item_data, current_stock=10)
            test_db.add(item)
        
        await test_db.commit()
        
        # Search for "computer"
        items, total = await service.get_items(search="computer")
        assert total == 2
        assert all("computer" in item.name.lower() for item in items)
    
    @pytest.mark.asyncio
    async def test_low_stock_items(self, test_db: AsyncSession):
        """Test getting low stock items"""
        service = InventoryService(test_db)
        
        # Create items with different stock levels
        items_data = [
            {'sku': 'LOW-001', 'name': 'Low Stock 1', 'current_stock': 2, 'available_stock': 2, 'min_stock_level': 10},
            {'sku': 'LOW-002', 'name': 'Low Stock 2', 'current_stock': 5, 'available_stock': 5, 'min_stock_level': 10},
            {'sku': 'GOOD-001', 'name': 'Good Stock', 'current_stock': 50, 'available_stock': 50, 'min_stock_level': 10},
        ]
        
        for item_data in items_data:
            item = Item(**item_data)
            test_db.add(item)
        
        await test_db.commit()
        
        # Get low stock items
        low_stock_items = await service.get_low_stock_items()
        assert len(low_stock_items) == 2
        assert all(item.is_low_stock for item in low_stock_items)
    
    @pytest.mark.asyncio
    async def test_create_stock_movement(self, test_db: AsyncSession, test_user: User):
        """Test creating stock movement through service"""
        service = InventoryService(test_db)
        
        # Create item
        item = Item(
            sku='MOVEMENT-001',
            name='Movement Test',
            current_stock=100,
            available_stock=100
        )
        test_db.add(item)
        await test_db.commit()
        await test_db.refresh(item)
        
        # Create stock movement
        movement_data = {
            'movement_type': StockMovementType.ISSUE,
            'quantity': -10,
            'notes': 'Test issue'
        }
        
        movement = await service.create_stock_movement(item.id, movement_data, test_user.id)
        
        assert movement.id is not None
        assert movement.item_id == item.id
        assert movement.quantity == -10
        assert movement.movement_type == StockMovementType.ISSUE
        
        # Verify item stock was updated
        await test_db.refresh(item)
        assert item.current_stock == 90


class TestInventoryAPI:
    """Test inventory API endpoints"""
    
    @pytest.mark.asyncio
    async def test_get_items_endpoint(self, client: AsyncClient, auth_headers: dict):
        """Test GET /api/v1/inventory/items endpoint"""
        response = await client.get("/api/v1/inventory/items", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert isinstance(data["items"], list)
    
    @pytest.mark.asyncio
    async def test_create_item_endpoint(self, client: AsyncClient, auth_headers: dict):
        """Test POST /api/v1/inventory/items endpoint"""
        item_data = {
            "sku": "API-001",
            "name": "API Test Item",
            "description": "Created via API",
            "cost_price": 25.00,
            "selling_price": 35.00,
            "current_stock": 75,
            "min_stock_level": 10,
            "reorder_point": 20,
            "reorder_quantity": 50
        }
        
        response = await client.post(
            "/api/v1/inventory/items",
            json=item_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["sku"] == "API-001"
        assert data["name"] == "API Test Item"
        assert data["current_stock"] == 75
    
    @pytest.mark.asyncio
    async def test_get_item_by_id_endpoint(self, client: AsyncClient, auth_headers: dict, test_item: Item):
        """Test GET /api/v1/inventory/items/{id} endpoint"""
        response = await client.get(
            f"/api/v1/inventory/items/{test_item.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_item.id
        assert data["sku"] == test_item.sku
    
    @pytest.mark.asyncio
    async def test_update_item_endpoint(self, client: AsyncClient, auth_headers: dict, test_item: Item):
        """Test PUT /api/v1/inventory/items/{id} endpoint"""
        update_data = {
            "name": "Updated Item Name",
            "description": "Updated description",
            "current_stock": 150
        }
        
        response = await client.put(
            f"/api/v1/inventory/items/{test_item.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Item Name"
        assert data["description"] == "Updated description"
        assert data["current_stock"] == 150
    
    @pytest.mark.asyncio
    async def test_create_stock_movement_endpoint(self, client: AsyncClient, auth_headers: dict, test_item: Item):
        """Test POST /api/v1/inventory/items/{id}/stock-movement endpoint"""
        movement_data = {
            "movement_type": "issue",
            "quantity": -5,
            "notes": "API test movement"
        }
        
        response = await client.post(
            f"/api/v1/inventory/items/{test_item.id}/stock-movement",
            json=movement_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["item_id"] == test_item.id
        assert data["quantity"] == -5
        assert data["movement_type"] == "issue"
    
    @pytest.mark.asyncio
    async def test_get_low_stock_items_endpoint(self, client: AsyncClient, auth_headers: dict):
        """Test GET /api/v1/inventory/low-stock endpoint"""
        response = await client.get("/api/v1/inventory/low-stock", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_unauthorized_access(self, client: AsyncClient):
        """Test that endpoints require authentication"""
        response = await client.get("/api/v1/inventory/items")
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_invalid_item_id(self, client: AsyncClient, auth_headers: dict):
        """Test accessing non-existent item"""
        response = await client.get("/api/v1/inventory/items/99999", headers=auth_headers)
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_duplicate_sku(self, client: AsyncClient, auth_headers: dict, test_item: Item):
        """Test creating item with duplicate SKU"""
        item_data = {
            "sku": test_item.sku,  # Use existing SKU
            "name": "Duplicate SKU Item",
            "current_stock": 10
        }
        
        response = await client.post(
            "/api/v1/inventory/items",
            json=item_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"].lower()
