# Warehouse Inventory Management System

A comprehensive, modern inventory management solution for small warehouses built with Python, featuring real-time stock tracking, automated order fulfillment, and error minimization.

## 🚀 Features

### Core Functionality
- **Real-time Inventory Tracking**: Live updates across all platforms with WebSocket integration
- **Automated Order Fulfillment**: Intelligent routing, picking optimization, and automated reordering
- **Error Minimization**: Barcode verification, cycle counting, and comprehensive audit trails
- **Multi-platform Support**: Desktop (PyQt6), Mobile (Kivy), and Web interfaces

### Advanced Features
- **Barcode/QR Code Scanning**: Integrated camera support for all platforms
- **Location Management**: Bin, shelf, and zone-based tracking
- **Supplier Integration**: Automated purchase orders and receiving workflows
- **Analytics & Reporting**: Real-time dashboards and comprehensive reports
- **Offline Support**: Local caching with automatic synchronization

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Desktop App   │    │   Mobile App    │    │    Web App      │
│    (PyQt6)      │    │    (Kivy)       │    │   (React)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      FastAPI Backend     │
                    │    (Python 3.11+)       │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     PostgreSQL DB        │
                    │     Redis Cache          │
                    └───────────────────────────┘
```

## 🛠️ Tech Stack

### Backend
- **FastAPI**: High-performance async Python web framework
- **PostgreSQL**: Primary database for persistent storage
- **Redis**: Caching and real-time pub/sub messaging
- **Celery**: Background task processing
- **SQLAlchemy**: ORM for database operations
- **Pydantic**: Data validation and serialization

### Desktop Application
- **PyQt6**: Modern, native GUI framework
- **OpenCV**: Camera integration for barcode scanning
- **pyzbar**: Barcode/QR code decoding
- **ReportLab**: PDF generation for labels and reports

### Mobile Application
- **Kivy**: Cross-platform Python mobile framework
- **Plyer**: Platform-specific features (camera, notifications)
- **Buildozer**: Android APK building

### DevOps & Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **Nginx**: Reverse proxy and load balancing
- **Prometheus**: Monitoring and metrics
- **Grafana**: Visualization and dashboards

## 📁 Project Structure

```
warehouse-inventory-system/
├── backend/                 # FastAPI backend service
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── services/       # Business logic
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── desktop/                # PyQt6 desktop application
│   ├── src/
│   │   ├── ui/             # UI components
│   │   ├── services/       # API clients
│   │   └── utils/          # Utilities
│   ├── tests/
│   └── requirements.txt
├── mobile/                 # Kivy mobile application
│   ├── src/
│   │   ├── screens/        # App screens
│   │   ├── widgets/        # Custom widgets
│   │   └── services/       # API clients
│   ├── buildozer.spec
│   └── requirements.txt
├── web/                    # React web application (optional)
├── docker/                 # Docker configurations
├── docs/                   # Documentation
└── scripts/                # Deployment and utility scripts
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose (recommended)

### Automated Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd warehouse-inventory-system
```

2. **Run the setup script**
```bash
python scripts/setup.py
```

3. **Start the system**
```bash
# Option 1: Using Docker (Recommended)
python scripts/run.py docker

# Option 2: Individual components
python scripts/run.py backend    # Start backend API
python scripts/run.py desktop    # Start desktop app
python scripts/run.py mobile     # Start mobile app

# Option 3: Start everything
python scripts/run.py all
```

### Manual Installation

1. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Desktop App Setup**
```bash
cd desktop
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Mobile App Setup**
```bash
cd mobile
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

4. **Database Setup**
```bash
# Using Docker
docker-compose up -d postgres redis

# Or install PostgreSQL and Redis locally
```

### First Run

1. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your settings
```

2. **Initialize database**
```bash
cd backend
alembic upgrade head
```

3. **Create admin user**
```bash
python scripts/create_admin.py
```

## 📱 Applications

### Desktop Application Features
- **Inventory Management**: Add, edit, and track inventory items
- **Barcode Scanning**: Integrated camera support for quick item identification
- **Order Processing**: Create and manage purchase/sales orders
- **Reporting**: Generate comprehensive reports and analytics
- **Label Printing**: Print barcode labels and shipping labels

### Mobile Application Features
- **Mobile Scanning**: Use device camera for barcode scanning
- **Real-time Updates**: Instant inventory updates and notifications
- **Order Picking**: Optimized picking routes and task management
- **Offline Mode**: Continue working without internet connection
- **Push Notifications**: Alerts for low stock, new orders, etc.

## 🔧 Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost/warehouse_db
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External Services
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

## 📊 Key Metrics & KPIs

- **Inventory Accuracy**: Real-time accuracy tracking
- **Order Fulfillment Time**: Average time from order to shipment
- **Pick Efficiency**: Items picked per hour
- **Stock Turnover**: Inventory turnover rates
- **Error Rates**: Picking and receiving error percentages

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Desktop app tests
cd desktop
pytest

# Mobile app tests
cd mobile
pytest
```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Desktop App Guide](docs/desktop.md)
- [Mobile App Guide](docs/mobile.md)
- [Deployment Guide](docs/deployment.md)
- [Contributing Guidelines](docs/contributing.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
