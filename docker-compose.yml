version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: warehouse_postgres
    environment:
      POSTGRES_DB: warehouse_db
      POSTGRES_USER: warehouse
      POSTGRES_PASSWORD: warehouse_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - warehouse_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U warehouse -d warehouse_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: warehouse_redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - warehouse_network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: warehouse_backend
    environment:
      - DATABASE_URL=postgresql+asyncpg://warehouse:warehouse_password@postgres:5432/warehouse_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
      - DEBUG=False
      - ALLOWED_HOSTS=["*"]
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - warehouse_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: warehouse_celery_worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://warehouse:warehouse_password@postgres:5432/warehouse_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - warehouse_network
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: warehouse_celery_beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://warehouse:warehouse_password@postgres:5432/warehouse_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - warehouse_network
    restart: unless-stopped

  # Flower for Celery Monitoring
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: warehouse_flower
    command: celery -A app.core.celery flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379
      - CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - warehouse_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: warehouse_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./backend/uploads:/var/www/uploads
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - warehouse_network
    restart: unless-stopped

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: warehouse_prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - warehouse_network
    restart: unless-stopped

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: warehouse_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - warehouse_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  warehouse_network:
    driver: bridge
