"""
Order models for purchase orders, sales orders, and transfers
"""
from sqlalchemy import Column, Integer, String, Text, Decimal, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from decimal import Decimal as PyDecimal

from app.core.database import Base


class OrderType(enum.Enum):
    """Types of orders"""
    PURCHASE = "purchase"      # Purchase order from supplier
    SALES = "sales"           # Sales order to customer
    TRANSFER = "transfer"     # Transfer between locations
    ADJUSTMENT = "adjustment" # Stock adjustment order


class OrderStatus(enum.Enum):
    """Order status workflow"""
    DRAFT = "draft"
    PENDING = "pending"
    APPROVED = "approved"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class Order(Base):
    """Orders (purchase, sales, transfer, adjustment)"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Order identification
    order_number = Column(String(50), unique=True, nullable=False)
    order_type = Column(Enum(OrderType), nullable=False)
    status = Column(Enum(OrderStatus), default=OrderStatus.DRAFT, nullable=False)
    
    # References
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True)
    supplier = relationship("Supplier")
    
    # User references
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    approved_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_by = relationship("User", foreign_keys=[created_by_id])
    approved_by = relationship("User", foreign_keys=[approved_by_id])
    
    # Location references (for transfers)
    from_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    to_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    from_location = relationship("Location", foreign_keys=[from_location_id])
    to_location = relationship("Location", foreign_keys=[to_location_id])
    
    # Order details
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Financial information
    subtotal = Column(Decimal(12, 2), default=0, nullable=False)
    tax_amount = Column(Decimal(12, 2), default=0, nullable=False)
    shipping_cost = Column(Decimal(10, 2), default=0, nullable=False)
    discount_amount = Column(Decimal(10, 2), default=0, nullable=False)
    total_amount = Column(Decimal(12, 2), default=0, nullable=False)
    currency = Column(String(3), default="USD", nullable=False)
    
    # Dates
    order_date = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    required_date = Column(DateTime(timezone=True), nullable=True)
    promised_date = Column(DateTime(timezone=True), nullable=True)
    shipped_date = Column(DateTime(timezone=True), nullable=True)
    delivered_date = Column(DateTime(timezone=True), nullable=True)
    
    # Shipping information
    shipping_address = Column(Text, nullable=True)
    shipping_method = Column(String(100), nullable=True)
    tracking_number = Column(String(100), nullable=True)
    
    # Priority and flags
    priority = Column(String(20), default="normal", nullable=False)  # low, normal, high, urgent
    is_rush_order = Column(Boolean, default=False, nullable=False)
    is_drop_ship = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    approved_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(id={self.id}, number='{self.order_number}', type='{self.order_type.value}', status='{self.status.value}')>"
    
    @property
    def total_items(self) -> int:
        """Get total number of items in the order"""
        return sum(item.quantity for item in self.order_items)
    
    @property
    def total_weight(self) -> PyDecimal:
        """Get total weight of the order"""
        total = PyDecimal('0')
        for item in self.order_items:
            if item.item and item.item.weight:
                total += item.item.weight * item.quantity
        return total
    
    def calculate_totals(self):
        """Calculate order totals from line items"""
        self.subtotal = sum(item.line_total for item in self.order_items)
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_cost - self.discount_amount
    
    @property
    def can_be_cancelled(self) -> bool:
        """Check if order can be cancelled"""
        return self.status in [OrderStatus.DRAFT, OrderStatus.PENDING, OrderStatus.APPROVED]
    
    @property
    def is_completed(self) -> bool:
        """Check if order is completed"""
        return self.status in [OrderStatus.COMPLETED, OrderStatus.DELIVERED]


class OrderItem(Base):
    """Individual items within an order"""
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Order reference
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    order = relationship("Order", back_populates="order_items")
    
    # Item reference
    item_id = Column(Integer, ForeignKey("items.id"), nullable=False)
    item = relationship("Item", back_populates="order_items")
    
    # Quantities
    quantity_ordered = Column(Integer, nullable=False)
    quantity_received = Column(Integer, default=0, nullable=False)
    quantity_shipped = Column(Integer, default=0, nullable=False)
    quantity_cancelled = Column(Integer, default=0, nullable=False)
    
    # Pricing
    unit_price = Column(Decimal(10, 2), nullable=False)
    discount_percent = Column(Decimal(5, 2), default=0, nullable=False)
    discount_amount = Column(Decimal(10, 2), default=0, nullable=False)
    line_total = Column(Decimal(12, 2), nullable=False)
    
    # Item details (snapshot at time of order)
    item_sku = Column(String(50), nullable=False)
    item_name = Column(String(200), nullable=False)
    item_description = Column(Text, nullable=True)
    
    # Batch/Serial tracking
    batch_number = Column(String(50), nullable=True)
    serial_numbers = Column(Text, nullable=True)  # JSON array for multiple serials
    expiry_date = Column(DateTime(timezone=True), nullable=True)
    
    # Location for receiving/shipping
    location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    location = relationship("Location")
    
    # Line item notes
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id={self.order_id}, item_sku='{self.item_sku}', qty={self.quantity_ordered})>"
    
    @property
    def quantity(self) -> int:
        """Alias for quantity_ordered for backward compatibility"""
        return self.quantity_ordered
    
    @property
    def quantity_pending(self) -> int:
        """Get quantity still pending (not received/shipped)"""
        if self.order.order_type == OrderType.PURCHASE:
            return max(0, self.quantity_ordered - self.quantity_received - self.quantity_cancelled)
        else:
            return max(0, self.quantity_ordered - self.quantity_shipped - self.quantity_cancelled)
    
    @property
    def is_fully_processed(self) -> bool:
        """Check if line item is fully processed"""
        return self.quantity_pending == 0
    
    def calculate_line_total(self):
        """Calculate line total with discounts"""
        subtotal = self.quantity_ordered * self.unit_price
        if self.discount_percent > 0:
            self.discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - self.discount_amount
