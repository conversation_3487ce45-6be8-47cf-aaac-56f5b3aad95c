"""
Configuration settings for the desktop application
"""
import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class AppConfig(BaseSettings):
    """Application configuration settings"""
    
    # Application info
    APP_NAME: str = "Warehouse Inventory Management"
    APP_VERSION: str = "1.0.0"
    
    # API Configuration
    API_BASE_URL: str = "http://localhost:8000/api/v1"
    API_TIMEOUT: int = 30
    API_RETRY_ATTEMPTS: int = 3
    
    # Authentication
    TOKEN_STORAGE_KEY: str = "wims_auth_token"
    REFRESH_TOKEN_KEY: str = "wims_refresh_token"
    AUTO_LOGIN: bool = True
    
    # Database (local SQLite for caching)
    LOCAL_DB_PATH: str = "data/local.db"
    CACHE_EXPIRY_HOURS: int = 24
    
    # UI Settings
    WINDOW_WIDTH: int = 1200
    WINDOW_HEIGHT: int = 800
    THEME: str = "light"  # light, dark
    LANGUAGE: str = "en"
    
    # Barcode Scanner
    CAMERA_INDEX: int = 0
    SCANNER_TIMEOUT: int = 10
    BARCODE_FORMATS: list = ["CODE128", "CODE39", "EAN13", "EAN8", "UPC_A", "UPC_E", "QR_CODE"]
    
    # Printing
    DEFAULT_PRINTER: Optional[str] = None
    LABEL_TEMPLATE: str = "default"
    PRINT_PREVIEW: bool = True
    
    # Data Sync
    SYNC_INTERVAL_MINUTES: int = 5
    OFFLINE_MODE: bool = False
    AUTO_SYNC: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # Performance
    CACHE_SIZE: int = 1000
    PAGINATION_SIZE: int = 50
    SEARCH_DEBOUNCE_MS: int = 300
    
    # Security
    SESSION_TIMEOUT_MINUTES: int = 480  # 8 hours
    LOCK_SCREEN_TIMEOUT_MINUTES: int = 15
    REQUIRE_PASSWORD_ON_SENSITIVE_OPERATIONS: bool = True
    
    # Features
    ENABLE_BARCODE_SCANNING: bool = True
    ENABLE_PRINTING: bool = True
    ENABLE_REPORTS: bool = True
    ENABLE_OFFLINE_MODE: bool = True
    ENABLE_NOTIFICATIONS: bool = True
    
    # Notifications
    SHOW_LOW_STOCK_ALERTS: bool = True
    SHOW_SYSTEM_NOTIFICATIONS: bool = True
    NOTIFICATION_SOUND: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            Path(self.LOCAL_DB_PATH).parent,
            Path(self.LOG_FILE).parent,
            Path("data"),
            Path("logs"),
            Path("cache"),
            Path("temp")
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def app_data_dir(self) -> Path:
        """Get application data directory"""
        if os.name == 'nt':  # Windows
            base_dir = Path(os.environ.get('APPDATA', Path.home() / 'AppData' / 'Roaming'))
        else:  # Linux/Mac
            base_dir = Path.home() / '.local' / 'share'
        
        app_dir = base_dir / 'WarehouseInventoryManagement'
        app_dir.mkdir(parents=True, exist_ok=True)
        return app_dir
    
    @property
    def config_dir(self) -> Path:
        """Get configuration directory"""
        if os.name == 'nt':  # Windows
            base_dir = Path(os.environ.get('APPDATA', Path.home() / 'AppData' / 'Roaming'))
        else:  # Linux/Mac
            base_dir = Path.home() / '.config'
        
        config_dir = base_dir / 'WarehouseInventoryManagement'
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir
    
    @property
    def cache_dir(self) -> Path:
        """Get cache directory"""
        if os.name == 'nt':  # Windows
            base_dir = Path(os.environ.get('LOCALAPPDATA', Path.home() / 'AppData' / 'Local'))
        else:  # Linux/Mac
            base_dir = Path.home() / '.cache'
        
        cache_dir = base_dir / 'WarehouseInventoryManagement'
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir
    
    def get_full_db_path(self) -> str:
        """Get full path to local database"""
        if Path(self.LOCAL_DB_PATH).is_absolute():
            return self.LOCAL_DB_PATH
        return str(self.app_data_dir / self.LOCAL_DB_PATH)
    
    def get_full_log_path(self) -> str:
        """Get full path to log file"""
        if Path(self.LOG_FILE).is_absolute():
            return self.LOG_FILE
        return str(self.app_data_dir / self.LOG_FILE)


# Global configuration instance
config = AppConfig()
