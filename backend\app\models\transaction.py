"""
Transaction models for financial and inventory transactions
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Decimal, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class TransactionType(enum.Enum):
    """Types of transactions"""
    RECEIPT = "receipt"           # Goods receipt
    SHIPMENT = "shipment"         # Goods shipment
    ADJUSTMENT = "adjustment"     # Inventory adjustment
    TRANSFER = "transfer"         # Location transfer
    CYCLE_COUNT = "cycle_count"   # Cycle count adjustment
    PURCHASE = "purchase"         # Purchase transaction
    SALE = "sale"                # Sales transaction
    RETURN = "return"            # Return transaction
    DAMAGE = "damage"            # Damage write-off
    OBSOLETE = "obsolete"        # Obsolete write-off


class Transaction(Base):
    """Financial and inventory transactions"""
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Transaction identification
    transaction_number = Column(String(50), unique=True, nullable=False)
    transaction_type = Column(Enum(TransactionType), nullable=False)
    
    # References
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)
    item_id = Column(Integer, ForeignKey("items.id"), nullable=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True)
    
    # Relationships
    order = relationship("Order")
    item = relationship("Item")
    supplier = relationship("Supplier")
    
    # User reference
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User")
    
    # Location references
    from_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    to_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    from_location = relationship("Location", foreign_keys=[from_location_id])
    to_location = relationship("Location", foreign_keys=[to_location_id])
    
    # Transaction details
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Quantities
    quantity = Column(Integer, nullable=False)
    unit_cost = Column(Decimal(10, 2), nullable=True)
    total_cost = Column(Decimal(12, 2), nullable=True)
    
    # Batch/Serial tracking
    batch_number = Column(String(50), nullable=True)
    serial_number = Column(String(50), nullable=True)
    expiry_date = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    is_posted = Column(Boolean, default=False, nullable=False)
    is_reversed = Column(Boolean, default=False, nullable=False)
    reversed_by_id = Column(Integer, ForeignKey("transactions.id"), nullable=True)
    reversed_by = relationship("Transaction", remote_side=[id])
    
    # Timestamps
    transaction_date = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    posted_date = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, number='{self.transaction_number}', type='{self.transaction_type.value}')>"
    
    @property
    def can_be_reversed(self) -> bool:
        """Check if transaction can be reversed"""
        return self.is_posted and not self.is_reversed
    
    def calculate_total_cost(self):
        """Calculate total cost from quantity and unit cost"""
        if self.unit_cost:
            self.total_cost = self.quantity * self.unit_cost
