"""
Supplier models for vendor management
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey, Decimal
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Supplier(Base):
    """Supplier/vendor model"""
    __tablename__ = "suppliers"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    legal_name = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    
    # Contact information
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    website = Column(String(200), nullable=True)
    
    # Address information
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # Business information
    tax_id = Column(String(50), nullable=True)
    registration_number = Column(String(50), nullable=True)
    payment_terms = Column(String(100), nullable=True)  # e.g., "Net 30"
    currency = Column(String(3), default="USD", nullable=False)
    
    # Performance metrics
    lead_time_days = Column(Integer, default=7, nullable=False)
    minimum_order_value = Column(Decimal(10, 2), nullable=True)
    rating = Column(Decimal(3, 2), nullable=True)  # 1.00 to 5.00
    
    # Status and flags
    is_active = Column(Boolean, default=True, nullable=False)
    is_preferred = Column(Boolean, default=False, nullable=False)
    is_approved = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_order_date = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    contacts = relationship("SupplierContact", back_populates="supplier", cascade="all, delete-orphan")
    items = relationship("Item", foreign_keys="Item.primary_supplier_id", back_populates="primary_supplier")
    
    def __repr__(self):
        return f"<Supplier(id={self.id}, code='{self.code}', name='{self.name}')>"
    
    @property
    def primary_contact(self):
        """Get the primary contact for this supplier"""
        return next((contact for contact in self.contacts if contact.is_primary), None)


class SupplierContact(Base):
    """Supplier contact persons"""
    __tablename__ = "supplier_contacts"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Supplier reference
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False)
    supplier = relationship("Supplier", back_populates="contacts")
    
    # Contact information
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    title = Column(String(100), nullable=True)
    department = Column(String(100), nullable=True)
    
    # Contact details
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    
    # Flags
    is_primary = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Communication preferences
    preferred_contact_method = Column(String(20), default="email", nullable=False)  # email, phone, mobile
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<SupplierContact(id={self.id}, name='{self.first_name} {self.last_name}', supplier='{self.supplier.name if self.supplier else None}')>"
    
    @property
    def full_name(self) -> str:
        """Get full name of the contact"""
        return f"{self.first_name} {self.last_name}"
