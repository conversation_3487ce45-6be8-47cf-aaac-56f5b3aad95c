"""
Main FastAPI application for Warehouse Inventory Management System
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import redis.asyncio as redis
import structlog
from typing import List

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router
from app.core.websocket_manager import WebSocketManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# WebSocket manager for real-time updates
websocket_manager = WebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Warehouse Inventory Management System")
    
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Initialize Redis connection
    app.state.redis = redis.from_url(settings.REDIS_URL)
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")
    await app.state.redis.close()
    logger.info("Application shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Warehouse Inventory Management API",
    description="A comprehensive inventory management system for small warehouses",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Warehouse Inventory Management System API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis connection
        await app.state.redis.ping()
        redis_status = "healthy"
    except Exception as e:
        logger.error("Redis health check failed", error=str(e))
        redis_status = "unhealthy"
    
    return {
        "status": "healthy",
        "services": {
            "redis": redis_status,
            "database": "healthy"  # Add actual DB health check
        }
    }


@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            logger.info("Received WebSocket message", client_id=client_id, data=data)
            
            # Echo message back (can be extended for specific message handling)
            await websocket_manager.send_personal_message(f"Echo: {data}", client_id)
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)
        logger.info("WebSocket client disconnected", client_id=client_id)


@app.websocket("/ws/inventory/updates")
async def inventory_updates_websocket(websocket: WebSocket):
    """WebSocket endpoint for inventory updates"""
    await websocket.accept()
    try:
        # Subscribe to Redis channel for inventory updates
        pubsub = app.state.redis.pubsub()
        await pubsub.subscribe("inventory_updates")
        
        async for message in pubsub.listen():
            if message["type"] == "message":
                await websocket.send_text(message["data"].decode())
                
    except WebSocketDisconnect:
        await pubsub.unsubscribe("inventory_updates")
        await pubsub.close()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
