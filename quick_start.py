#!/usr/bin/env python3
"""
Quick Start Script for Warehouse Inventory Management System
This script will guide you through the entire setup process step by step
"""
import os
import sys
import subprocess
import time
import platform
from pathlib import Path


def print_header():
    """Print welcome header"""
    print("=" * 60)
    print("🏭 WAREHOUSE INVENTORY MANAGEMENT SYSTEM")
    print("=" * 60)
    print("Welcome! This script will help you set up and run the system.")
    print("Please follow the steps carefully.\n")


def check_python():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.11+")
        print("Please install Python 3.11 or higher from https://python.org")
        return False


def check_git():
    """Check if Git is installed"""
    print("\n📦 Checking Git...")
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()} - OK")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Git not found")
    print("Please install Git from https://git-scm.com/downloads")
    return False


def check_docker():
    """Check if Docker is installed"""
    print("\n🐳 Checking Docker...")
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()} - OK")
            
            # Check Docker Compose
            result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {result.stdout.strip()} - OK")
                return True
            else:
                print("❌ Docker Compose not found")
                return False
    except FileNotFoundError:
        pass
    
    print("❌ Docker not found")
    print("Docker is recommended for easy setup. Install from https://docker.com")
    return False


def run_setup():
    """Run the setup script"""
    print("\n⚙️ Running automated setup...")
    print("This will install all dependencies and configure the system.")
    
    try:
        if platform.system() == "Windows":
            result = subprocess.run([sys.executable, "scripts/setup.py"], check=True)
        else:
            result = subprocess.run([sys.executable, "scripts/setup.py"], check=True)
        
        print("✅ Setup completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Setup failed with error: {e}")
        return False
    except FileNotFoundError:
        print("❌ Setup script not found. Make sure you're in the project directory.")
        return False


def start_docker_services():
    """Start Docker services"""
    print("\n🐳 Starting Docker services...")
    print("This will start the database, cache, and backend API.")
    
    try:
        # Start services
        subprocess.run([sys.executable, "scripts/run.py", "docker"], check=True)
        
        print("✅ Docker services started!")
        print("\nWaiting for services to be ready...")
        time.sleep(10)
        
        # Check status
        subprocess.run([sys.executable, "scripts/run.py", "status"], check=False)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Docker services: {e}")
        return False


def start_desktop_app():
    """Start desktop application"""
    print("\n💻 Starting Desktop Application...")
    
    response = input("Do you want to start the desktop app? (y/n): ").lower().strip()
    if response == 'y' or response == 'yes':
        try:
            print("Starting desktop application in background...")
            if platform.system() == "Windows":
                subprocess.Popen([sys.executable, "scripts/run.py", "desktop"], 
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                subprocess.Popen([sys.executable, "scripts/run.py", "desktop"])
            
            print("✅ Desktop application started!")
            return True
        except Exception as e:
            print(f"❌ Failed to start desktop app: {e}")
            return False
    else:
        print("⏭️ Skipping desktop application")
        return True


def start_mobile_app():
    """Start mobile application"""
    print("\n📱 Starting Mobile Application...")
    
    response = input("Do you want to start the mobile app? (y/n): ").lower().strip()
    if response == 'y' or response == 'yes':
        try:
            print("Starting mobile application in background...")
            if platform.system() == "Windows":
                subprocess.Popen([sys.executable, "scripts/run.py", "mobile"], 
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                subprocess.Popen([sys.executable, "scripts/run.py", "mobile"])
            
            print("✅ Mobile application started!")
            return True
        except Exception as e:
            print(f"❌ Failed to start mobile app: {e}")
            return False
    else:
        print("⏭️ Skipping mobile application")
        return True


def show_access_info():
    """Show how to access the system"""
    print("\n🌐 System Access Information:")
    print("-" * 40)
    print("🔗 Backend API: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("❤️ Health Check: http://localhost:8000/health")
    print("📊 Grafana Dashboard: http://localhost:3000 (admin/admin)")
    print("🌸 Flower Monitor: http://localhost:5555")
    print("\n🔑 Default Login Credentials:")
    print("Username: admin")
    print("Password: admin123")
    print("Role: Administrator")


def show_next_steps():
    """Show next steps"""
    print("\n🎯 Next Steps:")
    print("-" * 40)
    print("1. 🌐 Open http://localhost:8000/docs to see the API")
    print("2. 💻 Use the desktop app for warehouse operations")
    print("3. 📱 Use the mobile app for mobile scanning")
    print("4. 👥 Create additional users in the system")
    print("5. 📦 Add your inventory items and suppliers")
    print("6. 🏢 Configure your warehouse locations")
    print("7. 🧪 Test the complete workflow")


def run_tests():
    """Ask if user wants to run tests"""
    print("\n🧪 Testing")
    response = input("Do you want to run tests to verify everything works? (y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        print("Running tests...")
        try:
            subprocess.run([sys.executable, "scripts/run.py", "test"], check=True)
            print("✅ All tests passed!")
        except subprocess.CalledProcessError:
            print("❌ Some tests failed. Check the output above.")
    else:
        print("⏭️ Skipping tests")


def main():
    """Main function"""
    print_header()
    
    # Step 1: Check prerequisites
    print("Step 1: Checking Prerequisites")
    print("-" * 30)
    
    python_ok = check_python()
    git_ok = check_git()
    docker_ok = check_docker()
    
    if not python_ok or not git_ok:
        print("\n❌ Missing required prerequisites. Please install them and try again.")
        return 1
    
    if not docker_ok:
        print("\n⚠️ Docker not found. You can still run the system manually.")
        response = input("Continue without Docker? (y/n): ").lower().strip()
        if response != 'y' and response != 'yes':
            print("Please install Docker and try again.")
            return 1
    
    # Step 2: Setup
    print("\n" + "=" * 60)
    print("Step 2: System Setup")
    print("-" * 30)
    
    if not run_setup():
        print("\n❌ Setup failed. Please check the errors above.")
        return 1
    
    # Step 3: Start services
    if docker_ok:
        print("\n" + "=" * 60)
        print("Step 3: Starting Services")
        print("-" * 30)
        
        if not start_docker_services():
            print("\n❌ Failed to start services. Please check Docker.")
            return 1
    else:
        print("\n⚠️ Skipping Docker services. You'll need to start them manually.")
    
    # Step 4: Start applications
    print("\n" + "=" * 60)
    print("Step 4: Starting Applications")
    print("-" * 30)
    
    start_desktop_app()
    start_mobile_app()
    
    # Step 5: Run tests
    print("\n" + "=" * 60)
    print("Step 5: Testing (Optional)")
    print("-" * 30)
    
    run_tests()
    
    # Final information
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETE!")
    print("=" * 60)
    
    show_access_info()
    show_next_steps()
    
    print("\n📞 Need Help?")
    print("-" * 40)
    print("• Check SETUP_GUIDE.md for detailed instructions")
    print("• Run 'python scripts/run.py status' to check system status")
    print("• Run 'python scripts/run.py --help' for more commands")
    
    print("\n🎊 Enjoy your new Warehouse Inventory Management System!")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ Setup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
        sys.exit(1)
