# Database Configuration
DATABASE_URL=postgresql+asyncpg://warehouse:warehouse_password@localhost:5432/warehouse_db
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
ALLOWED_HOSTS=["localhost", "127.0.0.1", "0.0.0.0"]

# Email Configuration (for notifications)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Inventory Settings
LOW_STOCK_THRESHOLD=10
REORDER_POINT_MULTIPLIER=1.5

# Barcode Settings
BARCODE_PREFIX=WH
BARCODE_LENGTH=12

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=8001

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application
DEBUG=false
APP_NAME=Warehouse Inventory Management System
VERSION=1.0.0

# Desktop Application Settings
DESKTOP_API_BASE_URL=http://localhost:8000/api/v1
DESKTOP_API_TIMEOUT=30
DESKTOP_WINDOW_WIDTH=1200
DESKTOP_WINDOW_HEIGHT=800

# Mobile Application Settings
MOBILE_API_BASE_URL=http://localhost:8000/api/v1
MOBILE_API_TIMEOUT=30
MOBILE_SYNC_INTERVAL_MINUTES=5
MOBILE_OFFLINE_MODE=true
