"""
User model for authentication and authorization
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from datetime import datetime

from app.core.database import Base


class UserRole(enum.Enum):
    """User roles for role-based access control"""
    ADMIN = "admin"
    MANAGER = "manager"
    WAREHOUSE_STAFF = "warehouse_staff"
    VIEWER = "viewer"


class User(Base):
    """User model for authentication and authorization"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Role and permissions
    role = Column(Enum(UserRole), default=UserRole.WAREHOUSE_STAFF, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Profile information
    phone = Column(String(20), nullable=True)
    department = Column(String(50), nullable=True)
    employee_id = Column(String(20), unique=True, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # Security
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    password_changed_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    
    # Preferences
    preferences = Column(Text, nullable=True)  # JSON string for user preferences
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"
    
    @property
    def is_admin(self) -> bool:
        """Check if user is an admin"""
        return self.role == UserRole.ADMIN
    
    @property
    def is_manager(self) -> bool:
        """Check if user is a manager or admin"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    @property
    def can_manage_inventory(self) -> bool:
        """Check if user can manage inventory"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.WAREHOUSE_STAFF]
    
    @property
    def can_view_reports(self) -> bool:
        """Check if user can view reports"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
        permissions = {
            UserRole.ADMIN: [
                "manage_users", "manage_inventory", "manage_orders", 
                "manage_suppliers", "view_reports", "manage_settings"
            ],
            UserRole.MANAGER: [
                "manage_inventory", "manage_orders", "manage_suppliers", 
                "view_reports", "view_users"
            ],
            UserRole.WAREHOUSE_STAFF: [
                "manage_inventory", "view_orders", "update_stock"
            ],
            UserRole.VIEWER: [
                "view_inventory", "view_orders", "view_reports"
            ]
        }
        
        user_permissions = permissions.get(self.role, [])
        return permission in user_permissions
