# 🏭 Warehouse Inventory Management System - Setup Guide

## 📋 Prerequisites

### Required Software
- **Python 3.11+** - [Download here](https://www.python.org/downloads/)
- **Git** - [Download here](https://git-scm.com/downloads)
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/) (Recommended)

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: At least 2GB free space
- **OS**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)

## 🔧 Step 1: Check Prerequisites

### Check Python Version
```bash
python --version
# Should show Python 3.11.x or higher
```

### Check Git
```bash
git --version
# Should show git version
```

### Check Docker (Optional but Recommended)
```bash
docker --version
docker-compose --version
# Should show Docker and Docker Compose versions
```

## 📥 Step 2: Download the Project

### Option A: If you have the project files
```bash
# Navigate to your project directory
cd path/to/your/warehouse-inventory-system
```

### Option B: If cloning from repository
```bash
# Clone the repository
git clone <your-repository-url>
cd warehouse-inventory-system
```

## ⚙️ Step 3: Automated Setup (Recommended)

Run our automated setup script:

```bash
# Make setup script executable (Linux/Mac)
chmod +x scripts/setup.py

# Run automated setup
python scripts/setup.py
```

This will:
- ✅ Check all dependencies
- ✅ Create virtual environments
- ✅ Install all Python packages
- ✅ Setup environment files
- ✅ Create necessary directories

## 🐳 Step 4: Start with Docker (Easiest Method)

### Start All Services
```bash
# Start the entire system with Docker
python scripts/run.py docker
```

This command will:
- 🗄️ Start PostgreSQL database
- 🔄 Start Redis cache
- 🚀 Start FastAPI backend
- 📊 Start monitoring services
- 🌐 Setup reverse proxy

### Verify Services Are Running
```bash
# Check service status
python scripts/run.py status
```

You should see:
- ✅ Backend API: http://localhost:8000
- ✅ Database: Running
- ✅ Redis: Running

## 💻 Step 5: Start Desktop Application

### Option A: Using Run Script
```bash
# Start desktop app
python scripts/run.py desktop
```

### Option B: Manual Start
```bash
# Navigate to desktop directory
cd desktop

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Run the application
python main.py
```

## 📱 Step 6: Start Mobile Application

### Option A: Using Run Script
```bash
# Start mobile app
python scripts/run.py mobile
```

### Option B: Manual Start
```bash
# Navigate to mobile directory
cd mobile

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Run the application
python main.py
```

## 🌐 Step 7: Access the System

### Web Interfaces
- **API Documentation**: http://localhost:8000/docs
- **API Health Check**: http://localhost:8000/health
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Flower (Task Monitor)**: http://localhost:5555

### Desktop Application
- Should open automatically after running the desktop command
- Login with default credentials (will be created during setup)

### Mobile Application
- Should open in a window (development mode)
- For actual mobile deployment, see mobile deployment section

## 🔑 Step 8: First Login

### Default Admin User
- **Username**: admin
- **Password**: admin123
- **Role**: Administrator

### Create Additional Users
1. Login as admin
2. Go to User Management
3. Create new users with appropriate roles

## ✅ Step 9: Verify Everything Works

### Test Backend API
```bash
# Test API health
curl http://localhost:8000/health

# Should return: {"status":"healthy"}
```

### Test Desktop App
1. Open desktop application
2. Login with admin credentials
3. Navigate through different tabs
4. Try scanning a barcode (if camera available)

### Test Mobile App
1. Open mobile application
2. Login with same credentials
3. Test inventory scanning features

## 🛠️ Alternative Setup Methods

### Method 1: Manual Setup (Advanced Users)

#### Backend Setup
```bash
cd backend
python -m venv venv
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

pip install -r requirements.txt
```

#### Desktop Setup
```bash
cd desktop
python -m venv venv
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

pip install -r requirements.txt
```

#### Mobile Setup
```bash
cd mobile
python -m venv venv
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

pip install -r requirements.txt
```

### Method 2: Without Docker

#### Start PostgreSQL and Redis Locally
```bash
# Install PostgreSQL and Redis on your system
# Then update .env file with local connection strings

# Start backend manually
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 Configuration

### Environment Variables
Edit the `.env` file to customize:

```bash
# Database
DATABASE_URL=postgresql+asyncpg://warehouse:warehouse_password@localhost:5432/warehouse_db

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-in-production

# API Settings
API_BASE_URL=http://localhost:8000/api/v1
```

## 🧪 Step 10: Run Tests

### Test All Components
```bash
python scripts/run.py test
```

### Test Individual Components
```bash
# Backend tests
cd backend
source venv/bin/activate
pytest tests/ -v

# Desktop tests
cd desktop
source venv/bin/activate
pytest tests/ -v

# Mobile tests
cd mobile
source venv/bin/activate
pytest tests/ -v
```

## 🚨 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill processes using ports
# Windows:
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/Mac:
lsof -ti:8000 | xargs kill -9
```

#### Database Connection Issues
```bash
# Reset Docker containers
docker-compose down
docker-compose up -d postgres redis
# Wait 10 seconds
docker-compose up -d backend
```

#### Python Package Issues
```bash
# Recreate virtual environment
rm -rf venv  # or rmdir /s venv on Windows
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate
pip install --upgrade pip
pip install -r requirements.txt
```

#### Camera/Barcode Scanner Issues
```bash
# Install additional dependencies
pip install opencv-python-headless
# Check camera permissions in system settings
```

## 📊 Step 11: Monitor Your System

### Check System Status
```bash
python scripts/run.py status
```

### View Logs
```bash
# Docker logs
docker-compose logs -f backend

# Application logs
tail -f backend/logs/app.log
tail -f desktop/logs/app.log
tail -f mobile/logs/app.log
```

### Performance Monitoring
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **Flower**: http://localhost:5555

## 🎯 Next Steps

1. **Customize Settings**: Edit `.env` file for your environment
2. **Add Users**: Create warehouse staff accounts
3. **Import Data**: Add your inventory items and suppliers
4. **Configure Locations**: Set up your warehouse layout
5. **Test Workflows**: Try complete order fulfillment process
6. **Deploy to Production**: Use Docker for production deployment

## 📞 Getting Help

If you encounter issues:

1. **Check Logs**: Look at application logs for error messages
2. **Verify Prerequisites**: Ensure all required software is installed
3. **Check Ports**: Make sure required ports are not in use
4. **Restart Services**: Try stopping and starting services
5. **Clean Setup**: Remove virtual environments and recreate

## 🎉 Success!

If everything is working, you should have:
- ✅ Backend API running on http://localhost:8000
- ✅ Desktop application running
- ✅ Mobile application running
- ✅ Database and Redis services running
- ✅ Monitoring dashboards accessible

Your warehouse inventory management system is now ready to use!
