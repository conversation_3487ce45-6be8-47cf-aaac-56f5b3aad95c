"""
Location models for warehouse, zones, and storage locations
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Decimal
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Warehouse(Base):
    """Warehouse/facility model"""
    __tablename__ = "warehouses"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Address information
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # Contact information
    phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    manager_name = Column(String(100), nullable=True)
    
    # Physical properties
    total_area = Column(Decimal(10, 2), nullable=True)  # in square meters
    storage_capacity = Column(Integer, nullable=True)   # number of items
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    zones = relationship("Zone", back_populates="warehouse", cascade="all, delete-orphan")
    locations = relationship("Location", back_populates="warehouse")
    
    def __repr__(self):
        return f"<Warehouse(id={self.id}, code='{self.code}', name='{self.name}')>"


class Zone(Base):
    """Warehouse zones (receiving, storage, shipping, etc.)"""
    __tablename__ = "zones"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    code = Column(String(20), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Warehouse reference
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False)
    warehouse = relationship("Warehouse", back_populates="zones")
    
    # Zone properties
    zone_type = Column(String(50), nullable=True)  # receiving, storage, shipping, etc.
    temperature_controlled = Column(Boolean, default=False)
    temperature_min = Column(Decimal(5, 2), nullable=True)  # in Celsius
    temperature_max = Column(Decimal(5, 2), nullable=True)  # in Celsius
    
    # Physical properties
    area = Column(Decimal(10, 2), nullable=True)  # in square meters
    height = Column(Decimal(5, 2), nullable=True)  # in meters
    capacity = Column(Integer, nullable=True)      # number of items
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    locations = relationship("Location", back_populates="zone", cascade="all, delete-orphan")
    
    # Unique constraint on warehouse + code
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )
    
    def __repr__(self):
        return f"<Zone(id={self.id}, code='{self.code}', name='{self.name}')>"


class Location(Base):
    """Specific storage locations (bins, shelves, etc.)"""
    __tablename__ = "locations"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic information
    code = Column(String(50), nullable=False)  # e.g., A-01-01-01
    name = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    
    # Hierarchy
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False)
    zone_id = Column(Integer, ForeignKey("zones.id"), nullable=False)
    parent_location_id = Column(Integer, ForeignKey("locations.id"), nullable=True)
    
    # Relationships
    warehouse = relationship("Warehouse", back_populates="locations")
    zone = relationship("Zone", back_populates="locations")
    parent_location = relationship("Location", remote_side=[id], backref="child_locations")
    
    # Location properties
    location_type = Column(String(50), nullable=True)  # bin, shelf, rack, floor, etc.
    barcode = Column(String(50), unique=True, nullable=True)
    
    # Physical properties
    length = Column(Decimal(8, 3), nullable=True)  # in meters
    width = Column(Decimal(8, 3), nullable=True)   # in meters
    height = Column(Decimal(8, 3), nullable=True)  # in meters
    weight_capacity = Column(Decimal(10, 2), nullable=True)  # in kg
    volume_capacity = Column(Decimal(10, 3), nullable=True)  # in cubic meters
    
    # Current usage
    current_items = Column(Integer, default=0, nullable=False)
    current_weight = Column(Decimal(10, 2), default=0, nullable=False)
    current_volume = Column(Decimal(10, 3), default=0, nullable=False)
    
    # Picking information
    pick_sequence = Column(Integer, nullable=True)  # for optimized picking routes
    is_pickable = Column(Boolean, default=True, nullable=False)
    is_receivable = Column(Boolean, default=True, nullable=False)
    
    # Status and flags
    is_active = Column(Boolean, default=True, nullable=False)
    is_blocked = Column(Boolean, default=False, nullable=False)
    block_reason = Column(String(200), nullable=True)
    
    # Environmental conditions
    temperature_controlled = Column(Boolean, default=False)
    humidity_controlled = Column(Boolean, default=False)
    hazmat_approved = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_counted = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<Location(id={self.id}, code='{self.code}', zone='{self.zone.code if self.zone else None}')>"
    
    @property
    def full_code(self) -> str:
        """Get full location code including warehouse and zone"""
        if self.warehouse and self.zone:
            return f"{self.warehouse.code}-{self.zone.code}-{self.code}"
        return self.code
    
    @property
    def is_full(self) -> bool:
        """Check if location is at capacity"""
        if self.weight_capacity and self.current_weight >= self.weight_capacity:
            return True
        if self.volume_capacity and self.current_volume >= self.volume_capacity:
            return True
        return False
    
    @property
    def available_weight_capacity(self) -> Decimal:
        """Get available weight capacity"""
        if self.weight_capacity:
            return max(Decimal('0'), self.weight_capacity - self.current_weight)
        return Decimal('999999')  # Unlimited if no capacity set
    
    @property
    def available_volume_capacity(self) -> Decimal:
        """Get available volume capacity"""
        if self.volume_capacity:
            return max(Decimal('0'), self.volume_capacity - self.current_volume)
        return Decimal('999999')  # Unlimited if no capacity set
